/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  net.md_5.bungee.api.connection.ProxiedPlayer
 *  net.md_5.bungee.config.Configuration
 *  net.md_5.bungee.config.ConfigurationProvider
 *  net.md_5.bungee.config.YamlConfiguration
 */
package cn.lliiooll.bc.utils;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.data.PlayerChatDataOuterClass;
import cn.lliiooll.bc.utils.FileUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.config.Configuration;
import net.md_5.bungee.config.ConfigurationProvider;
import net.md_5.bungee.config.YamlConfiguration;

public class ConfigManager {
    // 缓存忽略列表，避免重复读取配置
    private static volatile List<String> cachedIgnoreList = null;
    private static volatile long lastConfigCheck = 0;
    private static final long CONFIG_CACHE_TIME = 30000; // 30秒缓存时间

    // 异步执行器，用于处理文件I/O操作
    private static final ScheduledExecutorService executor = Executors.newScheduledThreadPool(2);

    public static void init() {
        File dir;
        File dataCfgFile;
        File file;
        PKChatRecord instance = PKChatRecord.INSTANCE;
        if (!instance.getDataFolder().exists()) {
            instance.getDataFolder().mkdir();
        }
        if (!(file = new File(instance.getDataFolder(), "config.yml")).exists()) {
            try (InputStream in = instance.getResourceAsStream("config.yml");) {
                Files.copy(in, file.toPath(), new CopyOption[0]);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (!(dataCfgFile = new File(instance.getDataFolder(), "players.yml")).exists()) {
            try {
                dataCfgFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (!(dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data")).exists()) {
            dir.mkdirs();
        } else if (dir.listFiles() != null) {
            for (File file1 : dir.listFiles()) {
                file1.renameTo(new File(dir, file.getName().toLowerCase()));
            }
        }
    }

    public static Configuration getDefault() {
        try {
            return ConfigurationProvider.getProvider(YamlConfiguration.class)
                    .load(new File(PKChatRecord.INSTANCE.getDataFolder(), "config.yml"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void record(String message, ProxiedPlayer player) {
        try {
            String playerName;
            File dataFile;
            File dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            if (!(dataFile = new File(dir, (playerName = player.getName().toLowerCase()) + ".chat")).exists()) {
                dataFile.createNewFile();
                FileUtils.write(PlayerChatDataOuterClass.PlayerChatData.newBuilder().setName(playerName)
                        .setUuid(player.getUniqueId().toString())
                        .addChats(PlayerChatDataOuterClass.PlayerChatData.ChatData.newBuilder().setContent(message)
                                .setServer(player.getServer().getInfo().getName()).setTime(System.currentTimeMillis())
                                .build())
                        .build().toByteArray(), dataFile);
            } else {
                PlayerChatDataOuterClass.PlayerChatData data = PlayerChatDataOuterClass.PlayerChatData
                        .parseFrom(FileUtils.read(dataFile));
                ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData> chats = new ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData>(
                        data.getChatsList());
                chats.add(PlayerChatDataOuterClass.PlayerChatData.ChatData.newBuilder().setContent(message)
                        .setTime(System.currentTimeMillis()).setServer(player.getServer().getInfo().getName()).build());
                data = PlayerChatDataOuterClass.PlayerChatData.newBuilder().setUuid(data.getUuid())
                        .setName(data.getName()).addAllChats(chats).build();
                FileUtils.write(data.toByteArray(), dataFile);
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public static PlayerChatDataOuterClass.PlayerChatData getRecord(String playerName) {
        playerName = playerName.toLowerCase();
        File dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        if (!dir.exists()) {
            return null;
        }
        File dataFile = new File(dir, playerName + ".chat");
        if (!dataFile.exists()) {
            return null;
        }
        try {
            return PlayerChatDataOuterClass.PlayerChatData.parseFrom(FileUtils.read(dataFile));
        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 异步记录聊天消息，避免阻塞主线程
     */
    public static void recordAsync(String message, ProxiedPlayer player) {
        // 在异步线程中执行文件I/O操作
        executor.execute(() -> {
            try {
                record(message, player);
            } catch (Exception e) {
                PKChatRecord.INSTANCE.getLogger().warning("Failed to record chat message: " + e.getMessage());
            }
        });
    }

    /**
     * 获取缓存的忽略列表，提高性能
     */
    public static List<String> getCachedIgnoreList() {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否过期
        if (cachedIgnoreList == null || (currentTime - lastConfigCheck) > CONFIG_CACHE_TIME) {
            synchronized (ConfigManager.class) {
                // 双重检查锁定
                if (cachedIgnoreList == null || (currentTime - lastConfigCheck) > CONFIG_CACHE_TIME) {
                    try {
                        cachedIgnoreList = getDefault().getStringList("ignores");
                        lastConfigCheck = currentTime;
                    } catch (Exception e) {
                        PKChatRecord.INSTANCE.getLogger().warning("Failed to load ignore list: " + e.getMessage());
                        // 如果加载失败，返回空列表
                        cachedIgnoreList = new ArrayList<>();
                    }
                }
            }
        }

        return cachedIgnoreList;
    }

    /**
     * 清理资源，在插件禁用时调用
     */
    public static void shutdown() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
