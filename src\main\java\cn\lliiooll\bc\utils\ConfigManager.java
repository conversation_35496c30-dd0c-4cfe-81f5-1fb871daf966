/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  net.md_5.bungee.api.connection.ProxiedPlayer
 *  net.md_5.bungee.config.Configuration
 *  net.md_5.bungee.config.ConfigurationProvider
 *  net.md_5.bungee.config.YamlConfiguration
 */
package cn.lliiooll.bc.utils;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.data.PlayerChatData;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.config.Configuration;
import net.md_5.bungee.config.ConfigurationProvider;
import net.md_5.bungee.config.YamlConfiguration;

public class ConfigManager {
    // 缓存忽略列表，避免重复读取配置
    private static volatile List<String> cachedIgnoreList = new ArrayList<>();
    private static volatile long lastConfigCheck = 0;
    private static final long CONFIG_CACHE_TIME = 30000; // 30秒缓存时间

    // 异步执行器，用于处理文件I/O操作
    private static final ScheduledExecutorService executor = Executors.newScheduledThreadPool(2);

    // 批量写入队列
    private static final Map<String, List<PendingChatRecord>> pendingRecords = new ConcurrentHashMap<>();
    private static final int BATCH_SIZE = 10; // 批量写入大小
    private static final long BATCH_TIMEOUT = 5000; // 5秒超时

    public static void init() {
        File dir;
        File dataCfgFile;
        File file;
        PKChatRecord instance = PKChatRecord.INSTANCE;
        if (!instance.getDataFolder().exists()) {
            instance.getDataFolder().mkdir();
        }
        if (!(file = new File(instance.getDataFolder(), "config.yml")).exists()) {
            try (InputStream in = instance.getResourceAsStream("config.yml");) {
                Files.copy(in, file.toPath(), new CopyOption[0]);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (!(dataCfgFile = new File(instance.getDataFolder(), "players.yml")).exists()) {
            try {
                dataCfgFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (!(dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data")).exists()) {
            dir.mkdirs();
        } else if (dir.listFiles() != null) {
            for (File file1 : dir.listFiles()) {
                file1.renameTo(new File(dir, file.getName().toLowerCase()));
            }
        }

        // 预加载忽略列表
        preloadIgnoreList();
    }

    public static Configuration getDefault() {
        try {
            return ConfigurationProvider.getProvider(YamlConfiguration.class)
                    .load(new File(PKChatRecord.INSTANCE.getDataFolder(), "config.yml"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 预加载忽略列表，提高性能
     */
    private static void preloadIgnoreList() {
        try {
            cachedIgnoreList = getDefault().getStringList("ignores");
            lastConfigCheck = System.currentTimeMillis();
            PKChatRecord.INSTANCE.getLogger().info("§a预加载忽略列表成功，共 " + cachedIgnoreList.size() + " 项");
        } catch (Exception e) {
            PKChatRecord.INSTANCE.getLogger().warning("Failed to preload ignore list: " + e.getMessage());
            cachedIgnoreList = new ArrayList<>();
        }
    }

    public static void record(String message, ProxiedPlayer player) {
        try {
            String playerName = player.getName().toLowerCase();
            File dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
            if (!dir.exists()) {
                dir.mkdirs();
            }

            File dataFile = new File(dir, playerName + ".yml");
            Configuration playerData;

            if (!dataFile.exists()) {
                // 创建新的玩家数据文件
                dataFile.createNewFile();
                playerData = ConfigurationProvider.getProvider(YamlConfiguration.class).load(new String());
                playerData.set("name", playerName);
                playerData.set("uuid", player.getUniqueId().toString());
                playerData.set("chats", new ArrayList<>());
            } else {
                // 读取现有数据
                playerData = ConfigurationProvider.getProvider(YamlConfiguration.class).load(dataFile);
            }

            // 添加新的聊天记录
            List<Map<String, Object>> chats = (List<Map<String, Object>>) playerData.getList("chats",
                    new ArrayList<>());
            Map<String, Object> chatData = new HashMap<>();
            chatData.put("content", message);
            chatData.put("server", player.getServer().getInfo().getName());

            // 使用可读的时间格式而不是时间戳
            java.text.SimpleDateFormat timeFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            chatData.put("time", timeFormat.format(new java.util.Date()));
            chatData.put("timestamp", System.currentTimeMillis()); // 保留时间戳用于排序

            chats.add(chatData);
            playerData.set("chats", chats);

            // 保存文件
            ConfigurationProvider.getProvider(YamlConfiguration.class).save(playerData, dataFile);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static PlayerChatData getRecord(String playerName) {
        playerName = playerName.toLowerCase();
        File dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        if (!dir.exists()) {
            return null;
        }
        File dataFile = new File(dir, playerName + ".yml");
        if (!dataFile.exists()) {
            return null;
        }
        try {
            Configuration config = ConfigurationProvider.getProvider(YamlConfiguration.class).load(dataFile);
            return PlayerChatData.fromConfiguration(config);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 异步记录聊天消息，避免阻塞主线程
     * 使用批量写入提高性能
     */
    public static void recordAsync(String message, ProxiedPlayer player) {
        String playerName = player.getName().toLowerCase();
        PendingChatRecord record = new PendingChatRecord(message, player);

        // 添加到待写入队列
        pendingRecords.computeIfAbsent(playerName, k -> new ArrayList<>()).add(record);

        // 检查是否需要立即写入
        List<PendingChatRecord> playerRecords = pendingRecords.get(playerName);
        if (playerRecords.size() >= BATCH_SIZE) {
            // 异步批量写入
            executor.execute(() -> flushPendingRecords(playerName));
        } else {
            // 设置定时器，确保记录不会丢失
            executor.schedule(() -> {
                if (pendingRecords.containsKey(playerName)) {
                    flushPendingRecords(playerName);
                }
            }, BATCH_TIMEOUT, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 获取缓存的忽略列表，提高性能
     */
    public static List<String> getCachedIgnoreList() {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否过期
        if (cachedIgnoreList == null || (currentTime - lastConfigCheck) > CONFIG_CACHE_TIME) {
            synchronized (ConfigManager.class) {
                // 双重检查锁定
                if (cachedIgnoreList == null || (currentTime - lastConfigCheck) > CONFIG_CACHE_TIME) {
                    try {
                        cachedIgnoreList = getDefault().getStringList("ignores");
                        lastConfigCheck = currentTime;
                    } catch (Exception e) {
                        PKChatRecord.INSTANCE.getLogger().warning("Failed to load ignore list: " + e.getMessage());
                        // 如果加载失败，返回空列表
                        cachedIgnoreList = new ArrayList<>();
                    }
                }
            }
        }

        return cachedIgnoreList;
    }

    /**
     * 搜索聊天记录
     *
     * @param keyword    搜索关键词
     * @param playerName 玩家名（可选，为null时搜索所有玩家）
     * @param maxResults 最大结果数
     * @return 搜索结果列表
     */
    public static List<SearchResult> searchChatRecords(String keyword, String playerName, int maxResults) {
        List<SearchResult> results = new ArrayList<>();
        File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");

        if (!dataDir.exists()) {
            return results;
        }

        File[] chatFiles;
        if (playerName != null && !playerName.isEmpty()) {
            // 搜索指定玩家
            File playerFile = new File(dataDir, playerName.toLowerCase() + ".yml");
            chatFiles = playerFile.exists() ? new File[] { playerFile } : new File[0];
        } else {
            // 搜索所有玩家
            chatFiles = dataDir.listFiles((dir, name) -> name.endsWith(".yml"));
        }

        if (chatFiles == null) {
            return results;
        }

        for (File chatFile : chatFiles) {
            try {
                Configuration config = ConfigurationProvider.getProvider(YamlConfiguration.class).load(chatFile);
                PlayerChatData data = PlayerChatData.fromConfiguration(config);

                for (PlayerChatData.ChatData chat : data.getChatsList()) {
                    if (chat.getContent().toLowerCase().contains(keyword.toLowerCase())) {
                        results.add(new SearchResult(
                                data.getName(),
                                chat.getContent(),
                                chat.getServer(),
                                chat.getTime()));

                        if (results.size() >= maxResults) {
                            return results;
                        }
                    }
                }
            } catch (Exception e) {
                PKChatRecord.INSTANCE.getLogger().warning("Failed to read chat file: " + chatFile.getName());
            }
        }

        // 按时间倒序排列（最新的在前）
        results.sort((a, b) -> Long.compare(b.getTime(), a.getTime()));

        return results;
    }

    /**
     * 获取所有有聊天记录的玩家列表
     */
    public static List<String> getAllPlayersWithRecords() {
        List<String> players = new ArrayList<>();
        File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");

        if (!dataDir.exists()) {
            return players;
        }

        File[] chatFiles = dataDir.listFiles((dir, name) -> name.endsWith(".yml"));
        if (chatFiles != null) {
            for (File file : chatFiles) {
                String playerName = file.getName().replace(".yml", "");
                players.add(playerName);
            }
        }

        players.sort(String.CASE_INSENSITIVE_ORDER);
        return players;
    }

    /**
     * 搜索结果类
     */
    public static class SearchResult {
        private final String playerName;
        private final String message;
        private final String server;
        private final long time;

        public SearchResult(String playerName, String message, String server, long time) {
            this.playerName = playerName;
            this.message = message;
            this.server = server;
            this.time = time;
        }

        public String getPlayerName() {
            return playerName;
        }

        public String getMessage() {
            return message;
        }

        public String getServer() {
            return server;
        }

        public long getTime() {
            return time;
        }
    }

    /**
     * 待写入的聊天记录
     */
    private static class PendingChatRecord {
        final String message;
        final String playerName;
        final String uuid;
        final String server;
        final long time;

        PendingChatRecord(String message, ProxiedPlayer player) {
            this.message = message;
            this.playerName = player.getName().toLowerCase();
            this.uuid = player.getUniqueId().toString();
            this.server = player.getServer().getInfo().getName();
            this.time = System.currentTimeMillis();
        }
    }

    /**
     * 清理资源，在插件禁用时调用
     */
    public static void shutdown() {
        // 先处理所有待写入的记录
        flushAllPendingRecords();

        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 刷新所有待写入的记录
     */
    private static void flushAllPendingRecords() {
        for (String playerName : pendingRecords.keySet()) {
            flushPendingRecords(playerName);
        }
    }

    /**
     * 刷新指定玩家的待写入记录
     */
    private static void flushPendingRecords(String playerName) {
        List<PendingChatRecord> records = pendingRecords.remove(playerName);
        if (records != null && !records.isEmpty()) {
            try {
                batchWriteRecords(playerName, records);
            } catch (Exception e) {
                PKChatRecord.INSTANCE.getLogger()
                        .warning("Failed to flush pending records for " + playerName + ": " + e.getMessage());
            }
        }
    }

    /**
     * 批量写入聊天记录
     */
    private static void batchWriteRecords(String playerName, List<PendingChatRecord> records) throws Exception {
        File dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        if (!dir.exists()) {
            dir.mkdirs();
        }

        File dataFile = new File(dir, playerName + ".yml");
        Configuration playerData;

        if (!dataFile.exists()) {
            dataFile.createNewFile();
            playerData = ConfigurationProvider.getProvider(YamlConfiguration.class).load(new String());
            PendingChatRecord firstRecord = records.get(0);
            playerData.set("name", firstRecord.playerName);
            playerData.set("uuid", firstRecord.uuid);
            playerData.set("chats", new ArrayList<>());
        } else {
            playerData = ConfigurationProvider.getProvider(YamlConfiguration.class).load(dataFile);
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> chats = (List<Map<String, Object>>) playerData.getList("chats", new ArrayList<>());

        // 批量添加记录
        java.text.SimpleDateFormat timeFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (PendingChatRecord record : records) {
            Map<String, Object> chatData = new HashMap<>();
            chatData.put("content", record.message);
            chatData.put("server", record.server);
            chatData.put("time", timeFormat.format(new java.util.Date(record.time)));
            chatData.put("timestamp", record.time); // 保留时间戳用于排序
            chats.add(chatData);
        }

        playerData.set("chats", chats);
        ConfigurationProvider.getProvider(YamlConfiguration.class).save(playerData, dataFile);
    }
}
