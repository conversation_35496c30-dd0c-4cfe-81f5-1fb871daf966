/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  net.luckperms.api.model.user.User
 *  net.md_5.bungee.api.CommandSender
 *  net.md_5.bungee.api.connection.ProxiedPlayer
 *  net.md_5.bungee.api.plugin.Command
 */
package cn.lliiooll.bc.command;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.data.PlayerChatDataOuterClass;
import cn.lliiooll.bc.utils.ConfigManager;
import cn.lliiooll.bc.utils.StringUtils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.luckperms.api.model.user.User;
import net.md_5.bungee.api.CommandSender;
import net.md_5.bungee.api.chat.ClickEvent;
import net.md_5.bungee.api.chat.ComponentBuilder;
import net.md_5.bungee.api.chat.HoverEvent;
import net.md_5.bungee.api.chat.TextComponent;

import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.plugin.Command;
import net.md_5.bungee.api.plugin.TabExecutor;

public class ChatRecordCommand
        extends Command implements TabExecutor {
    public ChatRecordCommand() {
        super("chatrecord");
    }

    public void execute(CommandSender sender, String[] args) {
        // 添加调试信息
        PKChatRecord.INSTANCE.getLogger().info("ChatRecord command executed by: " + sender.getName() +
                " with args: " + String.join(" ", args));

        if (sender instanceof ProxiedPlayer) {
            ProxiedPlayer player = (ProxiedPlayer) sender;

            if (args.length > 0 && args.length < 3) {
                if (args.length == 1) {
                    this.printLines(player, 1, ConfigManager.getRecord(args[0]));
                } else if (StringUtils.isNumber(args[1])) {
                    this.printLines(player, Integer.parseInt(args[1]), ConfigManager.getRecord(args[0]));
                } else {
                    player.sendMessage("§c页数必须为数字");
                }
            } else {
                player.sendMessage("§c请使用 §7\"§b/chatrecord §e[§2玩家§e]§7 §e[§2页数§e]§7\" §c来查看聊天记录");
            }
        } else {
            sender.sendMessage("§c只有玩家可以使用此指令");
        }
    }

    @Override
    public Iterable<String> onTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：玩家名补全
            String partial = args[0].toLowerCase();

            // 获取所有在线玩家
            for (ProxiedPlayer player : PKChatRecord.INSTANCE.getProxy().getPlayers()) {
                String playerName = player.getName();
                if (playerName.toLowerCase().startsWith(partial)) {
                    completions.add(playerName);
                }
            }

            // 也可以从聊天记录文件中获取玩家名
            // 这里可以扫描 data 目录下的 .chat 文件

        } else if (args.length == 2) {
            // 第二个参数：页数补全
            String partial = args[1];
            if (partial.isEmpty() || "1".startsWith(partial)) {
                completions.add("1");
            }
            if (partial.isEmpty() || "2".startsWith(partial)) {
                completions.add("2");
            }
            if (partial.isEmpty() || "3".startsWith(partial)) {
                completions.add("3");
            }
        }

        return completions;
    }

    private void printLines(ProxiedPlayer player, int page, final PlayerChatDataOuterClass.PlayerChatData data) {
        int total;
        if (data == null) {
            player.sendMessage("\u00a7c\u73a9\u5bb6\u4e0d\u5728\u7ebf\u6216\u4e0d\u5b58\u5728!");
            return;
        }
        int pa = page - 1;
        ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData> datas = new ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData>(
                data.getChatsList());
        Collections.reverse(datas);
        int line = ConfigManager.getDefault().getInt("line", 5);
        total = datas.size() < 1 ? 0 : datas.size() / line + 1;
        if (pa < 0) {
            player.sendMessage("\u00a7c\u9875\u6570\u4e0d\u53ef\u4e3a \u00a7e" + page);
        } else if (page > total) {
            player.sendMessage("\u00a7c\u9875\u6570\u4e0d\u53ef\u4e3a \u00a7e" + page);
        } else {
            player.sendMessage("\u00a77\u00a7m==================\u00a7b[" + page + "/" + (total + "").split("\\.")[0]
                    + "]\u00a77\u00a7m==================");
            player.sendMessage(data.getName());
            SimpleDateFormat format = new SimpleDateFormat(ConfigManager.getDefault().getString("time"));
            for (int i = line * pa; i < line * pa + line && i < datas.size(); ++i) {
                final PlayerChatDataOuterClass.PlayerChatData.ChatData d = (PlayerChatDataOuterClass.PlayerChatData.ChatData) datas
                        .get(i);
                Date date = new Date(d.getTime());
                final String time = format.format(date);

                User user = PKChatRecord.luckPerms.getUserManager().getUser(data.getName());
                String prefix = "";
                if (user != null) {
                    prefix = user.getCachedData().getMetaData().getPrefix();
                }
                if (prefix == null) {
                    prefix = "";
                }
                final String finalPrefix = prefix;
                String f = StringUtils.replaceAll(ConfigManager.getDefault().getString("format"),
                        (Map<String, Object>) new HashMap<String, Object>() {
                            {
                                this.put("<time>", time);
                                this.put("<server>", ConfigManager.getDefault().getString("servers." + d.getServer()));
                                this.put("<msg>", d.getContent());
                                this.put("<name>", data.getName());
                                this.put("<prefix>", finalPrefix);
                            }
                        });
                // 显示所有消息（包括命令和聊天）
                player.sendMessage(f);
            }
            // 底部可点击的翻页栏
            sendClickablePagination(player, data.getName(), page, total);
        }
    }

    /**
     * 发送可点击的翻页栏
     */
    private void sendClickablePagination(ProxiedPlayer player, String playerName, int currentPage, int totalPages) {
        TextComponent pagination = new TextComponent();

        // 左边的分隔线
        TextComponent leftSeparator = new TextComponent("§7§m=======");
        pagination.addExtra(leftSeparator);

        // 上一页按钮
        if (currentPage > 1) {
            TextComponent prevButton = new TextComponent("§a[上一页]");
            prevButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND,
                    "/chatrecord " + playerName + " " + (currentPage - 1)));
            prevButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
                    new ComponentBuilder("§7点击查看第 " + (currentPage - 1) + " 页").create()));
            pagination.addExtra(prevButton);
        } else {
            pagination.addExtra(new TextComponent("§8[上一页]"));
        }

        // 页数信息
        TextComponent pageInfo = new TextComponent("§b[" + currentPage + "/" + totalPages + "]");
        pagination.addExtra(pageInfo);

        // 下一页按钮
        if (currentPage < totalPages) {
            TextComponent nextButton = new TextComponent("§a[下一页]");
            nextButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND,
                    "/chatrecord " + playerName + " " + (currentPage + 1)));
            nextButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
                    new ComponentBuilder("§7点击查看第 " + (currentPage + 1) + " 页").create()));
            pagination.addExtra(nextButton);
        } else {
            pagination.addExtra(new TextComponent("§8[下一页]"));
        }

        // 右边的分隔线
        TextComponent rightSeparator = new TextComponent("§7§m=======");
        pagination.addExtra(rightSeparator);

        player.sendMessage(pagination);
    }
}
