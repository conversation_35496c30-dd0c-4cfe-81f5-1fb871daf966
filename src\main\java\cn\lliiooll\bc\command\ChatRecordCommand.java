/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  net.luckperms.api.model.user.User
 *  net.md_5.bungee.api.CommandSender
 *  net.md_5.bungee.api.connection.ProxiedPlayer
 *  net.md_5.bungee.api.plugin.Command
 */
package cn.lliiooll.bc.command;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.data.PlayerChatDataOuterClass;
import cn.lliiooll.bc.utils.ConfigManager;
import cn.lliiooll.bc.utils.StringUtils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import net.luckperms.api.model.user.User;
import net.md_5.bungee.api.CommandSender;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.plugin.Command;

public class ChatRecordCommand
extends Command {
    public ChatRecordCommand() {
        super("chatrecord");
    }

    public void execute(CommandSender sender, String[] args) {
        if (sender instanceof ProxiedPlayer) {
            ProxiedPlayer player = (ProxiedPlayer)sender;
            if (player.hasPermission("PKChatrecord.staff") || player.hasPermission("PKChateecord.admin")) {
                if (args.length > 0 && args.length < 3) {
                    if (args.length == 1) {
                        this.printLines(player, 1, ConfigManager.getRecord(args[0]));
                    } else if (StringUtils.isNumber(args[1])) {
                        this.printLines(player, Integer.parseInt(args[1]), ConfigManager.getRecord(args[0]));
                    } else {
                        player.sendMessage("\u00a7c\u9875\u6570\u5fc5\u987b\u4e3a\u6570\u5b57");
                    }
                } else {
                    player.sendMessage("\u00a7c\u8bf7\u4f7f\u7528 \u00a77\"\u00a7b/chatrecord \u00a7e[\u00a72\u73a9\u5bb6\u00a7e]\u00a77 \u00a7e[\u00a72\u9875\u6570\u00a7e]\u00a77\" \u00a7c\u6765\u67e5\u770b\u804a\u5929\u8bb0\u5f55");
                }
            } else {
                player.sendMessage("\u00a7c\u4f60\u6ca1\u6709\u6743\u9650\u4f7f\u7528\u8fd9\u4e2a\u6307\u4ee4");
            }
        } else {
            sender.sendMessage("\u00a7c\u53ea\u6709\u73a9\u5bb6\u53ef\u4ee5\u4f7f\u7528\u6b64\u6307\u4ee4");
        }
    }

    private void printLines(ProxiedPlayer player, int page, final PlayerChatDataOuterClass.PlayerChatData data) {
        int total;
        if (data == null) {
            player.sendMessage("\u00a7c\u73a9\u5bb6\u4e0d\u5728\u7ebf\u6216\u4e0d\u5b58\u5728!");
            return;
        }
        int pa = page - 1;
        ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData> datas = new ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData>(data.getChatsList());
        Collections.reverse(datas);
        int line = ConfigManager.getDefault().getInt("line", 5);
        int n = total = datas.size() < 1 ? 0 : datas.size() / line + 1;
        if (pa < 0) {
            player.sendMessage("\u00a7c\u9875\u6570\u4e0d\u53ef\u4e3a \u00a7e" + page);
        } else if (page > total) {
            player.sendMessage("\u00a7c\u9875\u6570\u4e0d\u53ef\u4e3a \u00a7e" + page);
        } else {
            player.sendMessage("\u00a77\u00a7m==================\u00a7b[" + page + "/" + (total + "").split("\\.")[0] + "]\u00a77\u00a7m==================");
            player.sendMessage(data.getName());
            SimpleDateFormat format = new SimpleDateFormat(ConfigManager.getDefault().getString("time"));
            for (int i = line * pa; i < line * pa + line && i < datas.size(); ++i) {
                final PlayerChatDataOuterClass.PlayerChatData.ChatData d = (PlayerChatDataOuterClass.PlayerChatData.ChatData)datas.get(i);
                Date date = new Date(d.getTime());
                final String time = format.format(date);
                String message = d.getContent();
                User user = PKChatRecord.luckPerms.getUserManager().getUser(data.getName());
                String prefix = "";
                if (user != null) {
                    prefix = user.getCachedData().getMetaData().getPrefix();
                }
                if (prefix == null) {
                    prefix = "";
                }
                final String finalPrefix = prefix;
                String f = StringUtils.replaceAll(ConfigManager.getDefault().getString("format"), (Map<String, Object>)new HashMap<String, Object>(){
                    {
                        this.put("<time>", time);
                        this.put("<server>", ConfigManager.getDefault().getString("servers." + d.getServer()));
                        this.put("<msg>", d.getContent());
                        this.put("<name>", data.getName());
                        this.put("<prefix>", finalPrefix);
                    }
                });
                if (!player.hasPermission("PKChatrecord.admin")) {
                    if (message.startsWith("/")) continue;
                    player.sendMessage(f);
                    continue;
                }
                player.sendMessage(f);
            }
            player.sendMessage("\u00a77\u00a7m==================\u00a7b[" + page + "/" + (total + "").split("\\.")[0] + "]\u00a77\u00a7m==================");
        }
    }
}

