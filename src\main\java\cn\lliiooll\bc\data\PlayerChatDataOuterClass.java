/*
 * Decompiled with CFR 0.152.
 */
package cn.lliiooll.bc.data;

import com.google.protobuf.AbstractMessageLite;
import com.google.protobuf.AbstractParser;
import com.google.protobuf.ByteString;
import com.google.protobuf.CodedInputStream;
import com.google.protobuf.CodedOutputStream;
import com.google.protobuf.Descriptors;
import com.google.protobuf.ExtensionRegistry;
import com.google.protobuf.ExtensionRegistryLite;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Internal;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.Parser;
import com.google.protobuf.RepeatedFieldBuilderV3;
import com.google.protobuf.UnknownFieldSet;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public final class PlayerChatDataOuterClass {
    private static final Descriptors.Descriptor internal_static_cn_lliiooll_bc_data_PlayerChatData_descriptor;
    private static final GeneratedMessageV3.FieldAccessorTable internal_static_cn_lliiooll_bc_data_PlayerChatData_fieldAccessorTable;
    private static final Descriptors.Descriptor internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_descriptor;
    private static final GeneratedMessageV3.FieldAccessorTable internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_fieldAccessorTable;
    private static Descriptors.FileDescriptor descriptor;

    private PlayerChatDataOuterClass() {
    }

    public static void registerAllExtensions(ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(ExtensionRegistry registry) {
        PlayerChatDataOuterClass.registerAllExtensions((ExtensionRegistryLite)registry);
    }

    public static Descriptors.FileDescriptor getDescriptor() {
        return descriptor;
    }

    static {
        String[] descriptorData = new String[]{"\n\u0014PlayerChatData.proto\u0012\u0013cn.lliiooll.bc.data\"\u00a4\u0001\n\u000ePlayerChatData\u0012\f\n\u0004name\u0018\u0001 \u0001(\t\u0012\f\n\u0004uuid\u0018\u0002 \u0001(\t\u0012;\n\u0005chats\u0018\u0003 \u0003(\u000b2,.cn.lliiooll.bc.data.PlayerChatData.ChatData\u001a9\n\bChatData\u0012\u000f\n\u0007content\u0018\u0001 \u0001(\t\u0012\u000e\n\u0006server\u0018\u0002 \u0001(\t\u0012\f\n\u0004time\u0018\u0003 \u0001(\u0003b\u0006proto3"};
        Descriptors.FileDescriptor.InternalDescriptorAssigner assigner = new Descriptors.FileDescriptor.InternalDescriptorAssigner(){

            @Override
            public ExtensionRegistry assignDescriptors(Descriptors.FileDescriptor root) {
                descriptor = root;
                return null;
            }
        };
        Descriptors.FileDescriptor.internalBuildGeneratedFileFrom(descriptorData, new Descriptors.FileDescriptor[0], assigner);
        internal_static_cn_lliiooll_bc_data_PlayerChatData_descriptor = PlayerChatDataOuterClass.getDescriptor().getMessageTypes().get(0);
        internal_static_cn_lliiooll_bc_data_PlayerChatData_fieldAccessorTable = new GeneratedMessageV3.FieldAccessorTable(internal_static_cn_lliiooll_bc_data_PlayerChatData_descriptor, new String[]{"Name", "Uuid", "Chats"});
        internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_descriptor = internal_static_cn_lliiooll_bc_data_PlayerChatData_descriptor.getNestedTypes().get(0);
        internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_fieldAccessorTable = new GeneratedMessageV3.FieldAccessorTable(internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_descriptor, new String[]{"Content", "Server", "Time"});
    }

    public static final class PlayerChatData
    extends GeneratedMessageV3
    implements PlayerChatDataOrBuilder {
        private static final long serialVersionUID = 0L;
        private int bitField0_;
        public static final int NAME_FIELD_NUMBER = 1;
        private volatile Object name_;
        public static final int UUID_FIELD_NUMBER = 2;
        private volatile Object uuid_;
        public static final int CHATS_FIELD_NUMBER = 3;
        private List<ChatData> chats_;
        private byte memoizedIsInitialized = (byte)-1;
        private static final PlayerChatData DEFAULT_INSTANCE = new PlayerChatData();
        private static final Parser<PlayerChatData> PARSER = new AbstractParser<PlayerChatData>(){

            @Override
            public PlayerChatData parsePartialFrom(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
                return new PlayerChatData(input, extensionRegistry);
            }
        };

        private PlayerChatData(GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private PlayerChatData() {
            this.name_ = "";
            this.uuid_ = "";
            this.chats_ = Collections.emptyList();
        }

        @Override
        public final UnknownFieldSet getUnknownFields() {
            return this.unknownFields;
        }

        private PlayerChatData(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
            this();
            if (extensionRegistry == null) {
                throw new NullPointerException();
            }
            int mutable_bitField0_ = 0;
            UnknownFieldSet.Builder unknownFields = UnknownFieldSet.newBuilder();
            try {
                boolean done = false;
                block12: while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0: {
                            done = true;
                            continue block12;
                        }
                        case 10: {
                            String s = input.readStringRequireUtf8();
                            this.name_ = s;
                            continue block12;
                        }
                        case 18: {
                            String s = input.readStringRequireUtf8();
                            this.uuid_ = s;
                            continue block12;
                        }
                        case 26: {
                            if ((mutable_bitField0_ & 4) == 0) {
                                this.chats_ = new ArrayList<ChatData>();
                                mutable_bitField0_ |= 4;
                            }
                            this.chats_.add(input.readMessage(ChatData.parser(), extensionRegistry));
                            continue block12;
                        }
                    }
                    if (this.parseUnknownField(input, unknownFields, extensionRegistry, tag)) continue;
                    done = true;
                }
            }
            catch (InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            }
            catch (IOException e) {
                throw new InvalidProtocolBufferException(e).setUnfinishedMessage(this);
            }
            finally {
                if ((mutable_bitField0_ & 4) != 0) {
                    this.chats_ = Collections.unmodifiableList(this.chats_);
                }
                this.unknownFields = unknownFields.build();
                this.makeExtensionsImmutable();
            }
        }

        public static final Descriptors.Descriptor getDescriptor() {
            return internal_static_cn_lliiooll_bc_data_PlayerChatData_descriptor;
        }

        @Override
        protected GeneratedMessageV3.FieldAccessorTable internalGetFieldAccessorTable() {
            return internal_static_cn_lliiooll_bc_data_PlayerChatData_fieldAccessorTable.ensureFieldAccessorsInitialized(PlayerChatData.class, Builder.class);
        }

        @Override
        public String getName() {
            Object ref = this.name_;
            if (ref instanceof String) {
                return (String)ref;
            }
            ByteString bs = (ByteString)ref;
            String s = bs.toStringUtf8();
            this.name_ = s;
            return s;
        }

        @Override
        public ByteString getNameBytes() {
            Object ref = this.name_;
            if (ref instanceof String) {
                ByteString b = ByteString.copyFromUtf8((String)ref);
                this.name_ = b;
                return b;
            }
            return (ByteString)ref;
        }

        @Override
        public String getUuid() {
            Object ref = this.uuid_;
            if (ref instanceof String) {
                return (String)ref;
            }
            ByteString bs = (ByteString)ref;
            String s = bs.toStringUtf8();
            this.uuid_ = s;
            return s;
        }

        @Override
        public ByteString getUuidBytes() {
            Object ref = this.uuid_;
            if (ref instanceof String) {
                ByteString b = ByteString.copyFromUtf8((String)ref);
                this.uuid_ = b;
                return b;
            }
            return (ByteString)ref;
        }

        @Override
        public List<ChatData> getChatsList() {
            return this.chats_;
        }

        @Override
        public List<? extends ChatDataOrBuilder> getChatsOrBuilderList() {
            return this.chats_;
        }

        @Override
        public int getChatsCount() {
            return this.chats_.size();
        }

        @Override
        public ChatData getChats(int index) {
            return this.chats_.get(index);
        }

        @Override
        public ChatDataOrBuilder getChatsOrBuilder(int index) {
            return this.chats_.get(index);
        }

        @Override
        public final boolean isInitialized() {
            byte isInitialized = this.memoizedIsInitialized;
            if (isInitialized == 1) {
                return true;
            }
            if (isInitialized == 0) {
                return false;
            }
            this.memoizedIsInitialized = 1;
            return true;
        }

        @Override
        public void writeTo(CodedOutputStream output) throws IOException {
            if (!this.getNameBytes().isEmpty()) {
                GeneratedMessageV3.writeString(output, 1, this.name_);
            }
            if (!this.getUuidBytes().isEmpty()) {
                GeneratedMessageV3.writeString(output, 2, this.uuid_);
            }
            for (int i = 0; i < this.chats_.size(); ++i) {
                output.writeMessage(3, this.chats_.get(i));
            }
            this.unknownFields.writeTo(output);
        }

        @Override
        public int getSerializedSize() {
            int size = this.memoizedSize;
            if (size != -1) {
                return size;
            }
            size = 0;
            if (!this.getNameBytes().isEmpty()) {
                size += GeneratedMessageV3.computeStringSize(1, this.name_);
            }
            if (!this.getUuidBytes().isEmpty()) {
                size += GeneratedMessageV3.computeStringSize(2, this.uuid_);
            }
            for (int i = 0; i < this.chats_.size(); ++i) {
                size += CodedOutputStream.computeMessageSize(3, this.chats_.get(i));
            }
            this.memoizedSize = size += this.unknownFields.getSerializedSize();
            return size;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof PlayerChatData)) {
                return super.equals(obj);
            }
            PlayerChatData other = (PlayerChatData)obj;
            if (!this.getName().equals(other.getName())) {
                return false;
            }
            if (!this.getUuid().equals(other.getUuid())) {
                return false;
            }
            if (!this.getChatsList().equals(other.getChatsList())) {
                return false;
            }
            return this.unknownFields.equals(other.unknownFields);
        }

        @Override
        public int hashCode() {
            if (this.memoizedHashCode != 0) {
                return this.memoizedHashCode;
            }
            int hash = 41;
            hash = 19 * hash + PlayerChatData.getDescriptor().hashCode();
            hash = 37 * hash + 1;
            hash = 53 * hash + this.getName().hashCode();
            hash = 37 * hash + 2;
            hash = 53 * hash + this.getUuid().hashCode();
            if (this.getChatsCount() > 0) {
                hash = 37 * hash + 3;
                hash = 53 * hash + this.getChatsList().hashCode();
            }
            this.memoizedHashCode = hash = 29 * hash + this.unknownFields.hashCode();
            return hash;
        }

        public static PlayerChatData parseFrom(ByteBuffer data) throws InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static PlayerChatData parseFrom(ByteBuffer data, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static PlayerChatData parseFrom(ByteString data) throws InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static PlayerChatData parseFrom(ByteString data, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static PlayerChatData parseFrom(byte[] data) throws InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static PlayerChatData parseFrom(byte[] data, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static PlayerChatData parseFrom(InputStream input) throws IOException {
            return GeneratedMessageV3.parseWithIOException(PARSER, input);
        }

        public static PlayerChatData parseFrom(InputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
            return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static PlayerChatData parseDelimitedFrom(InputStream input) throws IOException {
            return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input);
        }

        public static PlayerChatData parseDelimitedFrom(InputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
            return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static PlayerChatData parseFrom(CodedInputStream input) throws IOException {
            return GeneratedMessageV3.parseWithIOException(PARSER, input);
        }

        public static PlayerChatData parseFrom(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
            return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
        }

        @Override
        public Builder newBuilderForType() {
            return PlayerChatData.newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(PlayerChatData prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        public static PlayerChatData getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        public static Parser<PlayerChatData> parser() {
            return PARSER;
        }

        public Parser<PlayerChatData> getParserForType() {
            return PARSER;
        }

        @Override
        public PlayerChatData getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

        public static final class ChatData
        extends GeneratedMessageV3
        implements ChatDataOrBuilder {
            private static final long serialVersionUID = 0L;
            public static final int CONTENT_FIELD_NUMBER = 1;
            private volatile Object content_;
            public static final int SERVER_FIELD_NUMBER = 2;
            private volatile Object server_;
            public static final int TIME_FIELD_NUMBER = 3;
            private long time_;
            private byte memoizedIsInitialized = (byte)-1;
            private static final ChatData DEFAULT_INSTANCE = new ChatData();
            private static final Parser<ChatData> PARSER = new AbstractParser<ChatData>(){

                @Override
                public ChatData parsePartialFrom(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
                    return new ChatData(input, extensionRegistry);
                }
            };

            private ChatData(GeneratedMessageV3.Builder<?> builder) {
                super(builder);
            }

            private ChatData() {
                this.content_ = "";
                this.server_ = "";
            }

            @Override
            public final UnknownFieldSet getUnknownFields() {
                return this.unknownFields;
            }

            private ChatData(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
                this();
                if (extensionRegistry == null) {
                    throw new NullPointerException();
                }
                boolean mutable_bitField0_ = false;
                UnknownFieldSet.Builder unknownFields = UnknownFieldSet.newBuilder();
                try {
                    boolean done = false;
                    block12: while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0: {
                                done = true;
                                continue block12;
                            }
                            case 10: {
                                String s = input.readStringRequireUtf8();
                                this.content_ = s;
                                continue block12;
                            }
                            case 18: {
                                String s = input.readStringRequireUtf8();
                                this.server_ = s;
                                continue block12;
                            }
                            case 24: {
                                this.time_ = input.readInt64();
                                continue block12;
                            }
                        }
                        if (this.parseUnknownField(input, unknownFields, extensionRegistry, tag)) continue;
                        done = true;
                    }
                }
                catch (InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(this);
                }
                catch (IOException e) {
                    throw new InvalidProtocolBufferException(e).setUnfinishedMessage(this);
                }
                finally {
                    this.unknownFields = unknownFields.build();
                    this.makeExtensionsImmutable();
                }
            }

            public static final Descriptors.Descriptor getDescriptor() {
                return internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_descriptor;
            }

            @Override
            protected GeneratedMessageV3.FieldAccessorTable internalGetFieldAccessorTable() {
                return internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_fieldAccessorTable.ensureFieldAccessorsInitialized(ChatData.class, Builder.class);
            }

            @Override
            public String getContent() {
                Object ref = this.content_;
                if (ref instanceof String) {
                    return (String)ref;
                }
                ByteString bs = (ByteString)ref;
                String s = bs.toStringUtf8();
                this.content_ = s;
                return s;
            }

            @Override
            public ByteString getContentBytes() {
                Object ref = this.content_;
                if (ref instanceof String) {
                    ByteString b = ByteString.copyFromUtf8((String)ref);
                    this.content_ = b;
                    return b;
                }
                return (ByteString)ref;
            }

            @Override
            public String getServer() {
                Object ref = this.server_;
                if (ref instanceof String) {
                    return (String)ref;
                }
                ByteString bs = (ByteString)ref;
                String s = bs.toStringUtf8();
                this.server_ = s;
                return s;
            }

            @Override
            public ByteString getServerBytes() {
                Object ref = this.server_;
                if (ref instanceof String) {
                    ByteString b = ByteString.copyFromUtf8((String)ref);
                    this.server_ = b;
                    return b;
                }
                return (ByteString)ref;
            }

            @Override
            public long getTime() {
                return this.time_;
            }

            @Override
            public final boolean isInitialized() {
                byte isInitialized = this.memoizedIsInitialized;
                if (isInitialized == 1) {
                    return true;
                }
                if (isInitialized == 0) {
                    return false;
                }
                this.memoizedIsInitialized = 1;
                return true;
            }

            @Override
            public void writeTo(CodedOutputStream output) throws IOException {
                if (!this.getContentBytes().isEmpty()) {
                    GeneratedMessageV3.writeString(output, 1, this.content_);
                }
                if (!this.getServerBytes().isEmpty()) {
                    GeneratedMessageV3.writeString(output, 2, this.server_);
                }
                if (this.time_ != 0L) {
                    output.writeInt64(3, this.time_);
                }
                this.unknownFields.writeTo(output);
            }

            @Override
            public int getSerializedSize() {
                int size = this.memoizedSize;
                if (size != -1) {
                    return size;
                }
                size = 0;
                if (!this.getContentBytes().isEmpty()) {
                    size += GeneratedMessageV3.computeStringSize(1, this.content_);
                }
                if (!this.getServerBytes().isEmpty()) {
                    size += GeneratedMessageV3.computeStringSize(2, this.server_);
                }
                if (this.time_ != 0L) {
                    size += CodedOutputStream.computeInt64Size(3, this.time_);
                }
                this.memoizedSize = size += this.unknownFields.getSerializedSize();
                return size;
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == this) {
                    return true;
                }
                if (!(obj instanceof ChatData)) {
                    return super.equals(obj);
                }
                ChatData other = (ChatData)obj;
                if (!this.getContent().equals(other.getContent())) {
                    return false;
                }
                if (!this.getServer().equals(other.getServer())) {
                    return false;
                }
                if (this.getTime() != other.getTime()) {
                    return false;
                }
                return this.unknownFields.equals(other.unknownFields);
            }

            @Override
            public int hashCode() {
                if (this.memoizedHashCode != 0) {
                    return this.memoizedHashCode;
                }
                int hash = 41;
                hash = 19 * hash + ChatData.getDescriptor().hashCode();
                hash = 37 * hash + 1;
                hash = 53 * hash + this.getContent().hashCode();
                hash = 37 * hash + 2;
                hash = 53 * hash + this.getServer().hashCode();
                hash = 37 * hash + 3;
                hash = 53 * hash + Internal.hashLong(this.getTime());
                this.memoizedHashCode = hash = 29 * hash + this.unknownFields.hashCode();
                return hash;
            }

            public static ChatData parseFrom(ByteBuffer data) throws InvalidProtocolBufferException {
                return PARSER.parseFrom(data);
            }

            public static ChatData parseFrom(ByteBuffer data, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
                return PARSER.parseFrom(data, extensionRegistry);
            }

            public static ChatData parseFrom(ByteString data) throws InvalidProtocolBufferException {
                return PARSER.parseFrom(data);
            }

            public static ChatData parseFrom(ByteString data, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
                return PARSER.parseFrom(data, extensionRegistry);
            }

            public static ChatData parseFrom(byte[] data) throws InvalidProtocolBufferException {
                return PARSER.parseFrom(data);
            }

            public static ChatData parseFrom(byte[] data, ExtensionRegistryLite extensionRegistry) throws InvalidProtocolBufferException {
                return PARSER.parseFrom(data, extensionRegistry);
            }

            public static ChatData parseFrom(InputStream input) throws IOException {
                return GeneratedMessageV3.parseWithIOException(PARSER, input);
            }

            public static ChatData parseFrom(InputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
                return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
            }

            public static ChatData parseDelimitedFrom(InputStream input) throws IOException {
                return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input);
            }

            public static ChatData parseDelimitedFrom(InputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
                return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input, extensionRegistry);
            }

            public static ChatData parseFrom(CodedInputStream input) throws IOException {
                return GeneratedMessageV3.parseWithIOException(PARSER, input);
            }

            public static ChatData parseFrom(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
                return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
            }

            @Override
            public Builder newBuilderForType() {
                return ChatData.newBuilder();
            }

            public static Builder newBuilder() {
                return DEFAULT_INSTANCE.toBuilder();
            }

            public static Builder newBuilder(ChatData prototype) {
                return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
            }

            @Override
            public Builder toBuilder() {
                return this == DEFAULT_INSTANCE ? new Builder() : new Builder().mergeFrom(this);
            }

            @Override
            protected Builder newBuilderForType(GeneratedMessageV3.BuilderParent parent) {
                Builder builder = new Builder(parent);
                return builder;
            }

            public static ChatData getDefaultInstance() {
                return DEFAULT_INSTANCE;
            }

            public static Parser<ChatData> parser() {
                return PARSER;
            }

            public Parser<ChatData> getParserForType() {
                return PARSER;
            }

            @Override
            public ChatData getDefaultInstanceForType() {
                return DEFAULT_INSTANCE;
            }

            public static final class Builder
            extends GeneratedMessageV3.Builder<Builder>
            implements ChatDataOrBuilder {
                private Object content_ = "";
                private Object server_ = "";
                private long time_;

                public static final Descriptors.Descriptor getDescriptor() {
                    return internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_descriptor;
                }

                @Override
                protected GeneratedMessageV3.FieldAccessorTable internalGetFieldAccessorTable() {
                    return internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_fieldAccessorTable.ensureFieldAccessorsInitialized(ChatData.class, Builder.class);
                }

                private Builder() {
                    this.maybeForceBuilderInitialization();
                }

                private Builder(GeneratedMessageV3.BuilderParent parent) {
                    super(parent);
                    this.maybeForceBuilderInitialization();
                }

                private void maybeForceBuilderInitialization() {
                    if (alwaysUseFieldBuilders) {
                        // empty if block
                    }
                }

                @Override
                public Builder clear() {
                    super.clear();
                    this.content_ = "";
                    this.server_ = "";
                    this.time_ = 0L;
                    return this;
                }

                @Override
                public Descriptors.Descriptor getDescriptorForType() {
                    return internal_static_cn_lliiooll_bc_data_PlayerChatData_ChatData_descriptor;
                }

                @Override
                public ChatData getDefaultInstanceForType() {
                    return ChatData.getDefaultInstance();
                }

                @Override
                public ChatData build() {
                    ChatData result = this.buildPartial();
                    if (!result.isInitialized()) {
                        throw Builder.newUninitializedMessageException(result);
                    }
                    return result;
                }

                @Override
                public ChatData buildPartial() {
                    ChatData result = new ChatData(this);
                    result.content_ = this.content_;
                    result.server_ = this.server_;
                    result.time_ = this.time_;
                    this.onBuilt();
                    return result;
                }

                @Override
                public Builder clone() {
                    return (Builder)super.clone();
                }

                @Override
                public Builder setField(Descriptors.FieldDescriptor field, Object value) {
                    return (Builder)super.setField(field, value);
                }

                @Override
                public Builder clearField(Descriptors.FieldDescriptor field) {
                    return (Builder)super.clearField(field);
                }

                @Override
                public Builder clearOneof(Descriptors.OneofDescriptor oneof) {
                    return (Builder)super.clearOneof(oneof);
                }

                @Override
                public Builder setRepeatedField(Descriptors.FieldDescriptor field, int index, Object value) {
                    return (Builder)super.setRepeatedField(field, index, value);
                }

                @Override
                public Builder addRepeatedField(Descriptors.FieldDescriptor field, Object value) {
                    return (Builder)super.addRepeatedField(field, value);
                }

                @Override
                public Builder mergeFrom(Message other) {
                    if (other instanceof ChatData) {
                        return this.mergeFrom((ChatData)other);
                    }
                    super.mergeFrom(other);
                    return this;
                }

                public Builder mergeFrom(ChatData other) {
                    if (other == ChatData.getDefaultInstance()) {
                        return this;
                    }
                    if (!other.getContent().isEmpty()) {
                        this.content_ = other.content_;
                        this.onChanged();
                    }
                    if (!other.getServer().isEmpty()) {
                        this.server_ = other.server_;
                        this.onChanged();
                    }
                    if (other.getTime() != 0L) {
                        this.setTime(other.getTime());
                    }
                    this.mergeUnknownFields(other.unknownFields);
                    this.onChanged();
                    return this;
                }

                @Override
                public final boolean isInitialized() {
                    return true;
                }

                @Override
                public Builder mergeFrom(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
                    ChatData parsedMessage = null;
                    try {
                        parsedMessage = (ChatData)PARSER.parsePartialFrom(input, extensionRegistry);
                    }
                    catch (InvalidProtocolBufferException e) {
                        parsedMessage = (ChatData)e.getUnfinishedMessage();
                        throw e.unwrapIOException();
                    }
                    finally {
                        if (parsedMessage != null) {
                            this.mergeFrom(parsedMessage);
                        }
                    }
                    return this;
                }

                @Override
                public String getContent() {
                    Object ref = this.content_;
                    if (!(ref instanceof String)) {
                        ByteString bs = (ByteString)ref;
                        String s = bs.toStringUtf8();
                        this.content_ = s;
                        return s;
                    }
                    return (String)ref;
                }

                @Override
                public ByteString getContentBytes() {
                    Object ref = this.content_;
                    if (ref instanceof String) {
                        ByteString b = ByteString.copyFromUtf8((String)ref);
                        this.content_ = b;
                        return b;
                    }
                    return (ByteString)ref;
                }

                public Builder setContent(String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    this.content_ = value;
                    this.onChanged();
                    return this;
                }

                public Builder clearContent() {
                    this.content_ = ChatData.getDefaultInstance().getContent();
                    this.onChanged();
                    return this;
                }

                public Builder setContentBytes(ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ChatData.checkByteStringIsUtf8(value);
                    this.content_ = value;
                    this.onChanged();
                    return this;
                }

                @Override
                public String getServer() {
                    Object ref = this.server_;
                    if (!(ref instanceof String)) {
                        ByteString bs = (ByteString)ref;
                        String s = bs.toStringUtf8();
                        this.server_ = s;
                        return s;
                    }
                    return (String)ref;
                }

                @Override
                public ByteString getServerBytes() {
                    Object ref = this.server_;
                    if (ref instanceof String) {
                        ByteString b = ByteString.copyFromUtf8((String)ref);
                        this.server_ = b;
                        return b;
                    }
                    return (ByteString)ref;
                }

                public Builder setServer(String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    this.server_ = value;
                    this.onChanged();
                    return this;
                }

                public Builder clearServer() {
                    this.server_ = ChatData.getDefaultInstance().getServer();
                    this.onChanged();
                    return this;
                }

                public Builder setServerBytes(ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ChatData.checkByteStringIsUtf8(value);
                    this.server_ = value;
                    this.onChanged();
                    return this;
                }

                @Override
                public long getTime() {
                    return this.time_;
                }

                public Builder setTime(long value) {
                    this.time_ = value;
                    this.onChanged();
                    return this;
                }

                public Builder clearTime() {
                    this.time_ = 0L;
                    this.onChanged();
                    return this;
                }

                @Override
                public final Builder setUnknownFields(UnknownFieldSet unknownFields) {
                    return (Builder)super.setUnknownFields(unknownFields);
                }

                @Override
                public final Builder mergeUnknownFields(UnknownFieldSet unknownFields) {
                    return (Builder)super.mergeUnknownFields(unknownFields);
                }
            }
        }

        public static final class Builder
        extends GeneratedMessageV3.Builder<Builder>
        implements PlayerChatDataOrBuilder {
            private int bitField0_;
            private Object name_ = "";
            private Object uuid_ = "";
            private List<ChatData> chats_ = Collections.emptyList();
            private RepeatedFieldBuilderV3<ChatData, ChatData.Builder, ChatDataOrBuilder> chatsBuilder_;

            public static final Descriptors.Descriptor getDescriptor() {
                return internal_static_cn_lliiooll_bc_data_PlayerChatData_descriptor;
            }

            @Override
            protected GeneratedMessageV3.FieldAccessorTable internalGetFieldAccessorTable() {
                return internal_static_cn_lliiooll_bc_data_PlayerChatData_fieldAccessorTable.ensureFieldAccessorsInitialized(PlayerChatData.class, Builder.class);
            }

            private Builder() {
                this.maybeForceBuilderInitialization();
            }

            private Builder(GeneratedMessageV3.BuilderParent parent) {
                super(parent);
                this.maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (alwaysUseFieldBuilders) {
                    this.getChatsFieldBuilder();
                }
            }

            @Override
            public Builder clear() {
                super.clear();
                this.name_ = "";
                this.uuid_ = "";
                if (this.chatsBuilder_ == null) {
                    this.chats_ = Collections.emptyList();
                    this.bitField0_ &= 0xFFFFFFFB;
                } else {
                    this.chatsBuilder_.clear();
                }
                return this;
            }

            @Override
            public Descriptors.Descriptor getDescriptorForType() {
                return internal_static_cn_lliiooll_bc_data_PlayerChatData_descriptor;
            }

            @Override
            public PlayerChatData getDefaultInstanceForType() {
                return PlayerChatData.getDefaultInstance();
            }

            @Override
            public PlayerChatData build() {
                PlayerChatData result = this.buildPartial();
                if (!result.isInitialized()) {
                    throw Builder.newUninitializedMessageException(result);
                }
                return result;
            }

            @Override
            public PlayerChatData buildPartial() {
                PlayerChatData result = new PlayerChatData(this);
                int from_bitField0_ = this.bitField0_;
                int to_bitField0_ = 0;
                result.name_ = this.name_;
                result.uuid_ = this.uuid_;
                if (this.chatsBuilder_ == null) {
                    if ((this.bitField0_ & 4) != 0) {
                        this.chats_ = Collections.unmodifiableList(this.chats_);
                        this.bitField0_ &= 0xFFFFFFFB;
                    }
                    result.chats_ = this.chats_;
                } else {
                    result.chats_ = this.chatsBuilder_.build();
                }
                result.bitField0_ = to_bitField0_;
                this.onBuilt();
                return result;
            }

            @Override
            public Builder clone() {
                return (Builder)super.clone();
            }

            @Override
            public Builder setField(Descriptors.FieldDescriptor field, Object value) {
                return (Builder)super.setField(field, value);
            }

            @Override
            public Builder clearField(Descriptors.FieldDescriptor field) {
                return (Builder)super.clearField(field);
            }

            @Override
            public Builder clearOneof(Descriptors.OneofDescriptor oneof) {
                return (Builder)super.clearOneof(oneof);
            }

            @Override
            public Builder setRepeatedField(Descriptors.FieldDescriptor field, int index, Object value) {
                return (Builder)super.setRepeatedField(field, index, value);
            }

            @Override
            public Builder addRepeatedField(Descriptors.FieldDescriptor field, Object value) {
                return (Builder)super.addRepeatedField(field, value);
            }

            @Override
            public Builder mergeFrom(Message other) {
                if (other instanceof PlayerChatData) {
                    return this.mergeFrom((PlayerChatData)other);
                }
                super.mergeFrom(other);
                return this;
            }

            public Builder mergeFrom(PlayerChatData other) {
                if (other == PlayerChatData.getDefaultInstance()) {
                    return this;
                }
                if (!other.getName().isEmpty()) {
                    this.name_ = other.name_;
                    this.onChanged();
                }
                if (!other.getUuid().isEmpty()) {
                    this.uuid_ = other.uuid_;
                    this.onChanged();
                }
                if (this.chatsBuilder_ == null) {
                    if (!other.chats_.isEmpty()) {
                        if (this.chats_.isEmpty()) {
                            this.chats_ = other.chats_;
                            this.bitField0_ &= 0xFFFFFFFB;
                        } else {
                            this.ensureChatsIsMutable();
                            this.chats_.addAll(other.chats_);
                        }
                        this.onChanged();
                    }
                } else if (!other.chats_.isEmpty()) {
                    if (this.chatsBuilder_.isEmpty()) {
                        this.chatsBuilder_.dispose();
                        this.chatsBuilder_ = null;
                        this.chats_ = other.chats_;
                        this.bitField0_ &= 0xFFFFFFFB;
                        this.chatsBuilder_ = alwaysUseFieldBuilders ? this.getChatsFieldBuilder() : null;
                    } else {
                        this.chatsBuilder_.addAllMessages(other.chats_);
                    }
                }
                this.mergeUnknownFields(other.unknownFields);
                this.onChanged();
                return this;
            }

            @Override
            public final boolean isInitialized() {
                return true;
            }

            @Override
            public Builder mergeFrom(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
                PlayerChatData parsedMessage = null;
                try {
                    parsedMessage = (PlayerChatData)PARSER.parsePartialFrom(input, extensionRegistry);
                }
                catch (InvalidProtocolBufferException e) {
                    parsedMessage = (PlayerChatData)e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                }
                finally {
                    if (parsedMessage != null) {
                        this.mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            @Override
            public String getName() {
                Object ref = this.name_;
                if (!(ref instanceof String)) {
                    ByteString bs = (ByteString)ref;
                    String s = bs.toStringUtf8();
                    this.name_ = s;
                    return s;
                }
                return (String)ref;
            }

            @Override
            public ByteString getNameBytes() {
                Object ref = this.name_;
                if (ref instanceof String) {
                    ByteString b = ByteString.copyFromUtf8((String)ref);
                    this.name_ = b;
                    return b;
                }
                return (ByteString)ref;
            }

            public Builder setName(String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                this.name_ = value;
                this.onChanged();
                return this;
            }

            public Builder clearName() {
                this.name_ = PlayerChatData.getDefaultInstance().getName();
                this.onChanged();
                return this;
            }

            public Builder setNameBytes(ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                PlayerChatData.checkByteStringIsUtf8(value);
                this.name_ = value;
                this.onChanged();
                return this;
            }

            @Override
            public String getUuid() {
                Object ref = this.uuid_;
                if (!(ref instanceof String)) {
                    ByteString bs = (ByteString)ref;
                    String s = bs.toStringUtf8();
                    this.uuid_ = s;
                    return s;
                }
                return (String)ref;
            }

            @Override
            public ByteString getUuidBytes() {
                Object ref = this.uuid_;
                if (ref instanceof String) {
                    ByteString b = ByteString.copyFromUtf8((String)ref);
                    this.uuid_ = b;
                    return b;
                }
                return (ByteString)ref;
            }

            public Builder setUuid(String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                this.uuid_ = value;
                this.onChanged();
                return this;
            }

            public Builder clearUuid() {
                this.uuid_ = PlayerChatData.getDefaultInstance().getUuid();
                this.onChanged();
                return this;
            }

            public Builder setUuidBytes(ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                PlayerChatData.checkByteStringIsUtf8(value);
                this.uuid_ = value;
                this.onChanged();
                return this;
            }

            private void ensureChatsIsMutable() {
                if ((this.bitField0_ & 4) == 0) {
                    this.chats_ = new ArrayList<ChatData>(this.chats_);
                    this.bitField0_ |= 4;
                }
            }

            @Override
            public List<ChatData> getChatsList() {
                if (this.chatsBuilder_ == null) {
                    return Collections.unmodifiableList(this.chats_);
                }
                return this.chatsBuilder_.getMessageList();
            }

            @Override
            public int getChatsCount() {
                if (this.chatsBuilder_ == null) {
                    return this.chats_.size();
                }
                return this.chatsBuilder_.getCount();
            }

            @Override
            public ChatData getChats(int index) {
                if (this.chatsBuilder_ == null) {
                    return this.chats_.get(index);
                }
                return this.chatsBuilder_.getMessage(index);
            }

            public Builder setChats(int index, ChatData value) {
                if (this.chatsBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    this.ensureChatsIsMutable();
                    this.chats_.set(index, value);
                    this.onChanged();
                } else {
                    this.chatsBuilder_.setMessage(index, value);
                }
                return this;
            }

            public Builder setChats(int index, ChatData.Builder builderForValue) {
                if (this.chatsBuilder_ == null) {
                    this.ensureChatsIsMutable();
                    this.chats_.set(index, builderForValue.build());
                    this.onChanged();
                } else {
                    this.chatsBuilder_.setMessage(index, builderForValue.build());
                }
                return this;
            }

            public Builder addChats(ChatData value) {
                if (this.chatsBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    this.ensureChatsIsMutable();
                    this.chats_.add(value);
                    this.onChanged();
                } else {
                    this.chatsBuilder_.addMessage(value);
                }
                return this;
            }

            public Builder addChats(int index, ChatData value) {
                if (this.chatsBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    this.ensureChatsIsMutable();
                    this.chats_.add(index, value);
                    this.onChanged();
                } else {
                    this.chatsBuilder_.addMessage(index, value);
                }
                return this;
            }

            public Builder addChats(ChatData.Builder builderForValue) {
                if (this.chatsBuilder_ == null) {
                    this.ensureChatsIsMutable();
                    this.chats_.add(builderForValue.build());
                    this.onChanged();
                } else {
                    this.chatsBuilder_.addMessage(builderForValue.build());
                }
                return this;
            }

            public Builder addChats(int index, ChatData.Builder builderForValue) {
                if (this.chatsBuilder_ == null) {
                    this.ensureChatsIsMutable();
                    this.chats_.add(index, builderForValue.build());
                    this.onChanged();
                } else {
                    this.chatsBuilder_.addMessage(index, builderForValue.build());
                }
                return this;
            }

            public Builder addAllChats(Iterable<? extends ChatData> values) {
                if (this.chatsBuilder_ == null) {
                    this.ensureChatsIsMutable();
                    AbstractMessageLite.Builder.addAll(values, this.chats_);
                    this.onChanged();
                } else {
                    this.chatsBuilder_.addAllMessages(values);
                }
                return this;
            }

            public Builder clearChats() {
                if (this.chatsBuilder_ == null) {
                    this.chats_ = Collections.emptyList();
                    this.bitField0_ &= 0xFFFFFFFB;
                    this.onChanged();
                } else {
                    this.chatsBuilder_.clear();
                }
                return this;
            }

            public Builder removeChats(int index) {
                if (this.chatsBuilder_ == null) {
                    this.ensureChatsIsMutable();
                    this.chats_.remove(index);
                    this.onChanged();
                } else {
                    this.chatsBuilder_.remove(index);
                }
                return this;
            }

            public ChatData.Builder getChatsBuilder(int index) {
                return this.getChatsFieldBuilder().getBuilder(index);
            }

            @Override
            public ChatDataOrBuilder getChatsOrBuilder(int index) {
                if (this.chatsBuilder_ == null) {
                    return this.chats_.get(index);
                }
                return this.chatsBuilder_.getMessageOrBuilder(index);
            }

            @Override
            public List<? extends ChatDataOrBuilder> getChatsOrBuilderList() {
                if (this.chatsBuilder_ != null) {
                    return this.chatsBuilder_.getMessageOrBuilderList();
                }
                return Collections.unmodifiableList(this.chats_);
            }

            public ChatData.Builder addChatsBuilder() {
                return this.getChatsFieldBuilder().addBuilder(ChatData.getDefaultInstance());
            }

            public ChatData.Builder addChatsBuilder(int index) {
                return this.getChatsFieldBuilder().addBuilder(index, ChatData.getDefaultInstance());
            }

            public List<ChatData.Builder> getChatsBuilderList() {
                return this.getChatsFieldBuilder().getBuilderList();
            }

            private RepeatedFieldBuilderV3<ChatData, ChatData.Builder, ChatDataOrBuilder> getChatsFieldBuilder() {
                if (this.chatsBuilder_ == null) {
                    this.chatsBuilder_ = new RepeatedFieldBuilderV3(this.chats_, (this.bitField0_ & 4) != 0, this.getParentForChildren(), this.isClean());
                    this.chats_ = null;
                }
                return this.chatsBuilder_;
            }

            @Override
            public final Builder setUnknownFields(UnknownFieldSet unknownFields) {
                return (Builder)super.setUnknownFields(unknownFields);
            }

            @Override
            public final Builder mergeUnknownFields(UnknownFieldSet unknownFields) {
                return (Builder)super.mergeUnknownFields(unknownFields);
            }
        }

        public static interface ChatDataOrBuilder
        extends MessageOrBuilder {
            public String getContent();

            public ByteString getContentBytes();

            public String getServer();

            public ByteString getServerBytes();

            public long getTime();
        }
    }

    public static interface PlayerChatDataOrBuilder
    extends MessageOrBuilder {
        public String getName();

        public ByteString getNameBytes();

        public String getUuid();

        public ByteString getUuidBytes();

        public List<PlayerChatData.ChatData> getChatsList();

        public PlayerChatData.ChatData getChats(int var1);

        public int getChatsCount();

        public List<? extends PlayerChatData.ChatDataOrBuilder> getChatsOrBuilderList();

        public PlayerChatData.ChatDataOrBuilder getChatsOrBuilder(int var1);
    }
}

