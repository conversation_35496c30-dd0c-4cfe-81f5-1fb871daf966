/*
 * Decompiled with CFR 0.152.
 */
package com.google.protobuf;

import com.google.protobuf.ByteString;
import com.google.protobuf.CodedOutputStream;
import com.google.protobuf.ExtensionRegistryLite;
import com.google.protobuf.Internal;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageLite;
import com.google.protobuf.Parser;
import com.google.protobuf.UnsafeUtil;
import com.google.protobuf.Utf8;
import com.google.protobuf.WireFormat;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class CodedInputStream {
    private static final int DEFAULT_BUFFER_SIZE = 4096;
    private static final int DEFAULT_RECURSION_LIMIT = 100;
    private static final int DEFAULT_SIZE_LIMIT = Integer.MAX_VALUE;
    int recursionDepth;
    int recursionLimit = 100;
    int sizeLimit = Integer.MAX_VALUE;
    private boolean explicitDiscardUnknownFields = false;
    private static volatile boolean proto3DiscardUnknownFieldsDefault = true;

    public static CodedInputStream newInstance(InputStream input) {
        return CodedInputStream.newInstance(input, 4096);
    }

    static CodedInputStream newInstance(InputStream input, int bufferSize) {
        if (input == null) {
            return CodedInputStream.newInstance(Internal.EMPTY_BYTE_ARRAY);
        }
        return new StreamDecoder(input, bufferSize);
    }

    public static CodedInputStream newInstance(byte[] buf) {
        return CodedInputStream.newInstance(buf, 0, buf.length);
    }

    public static CodedInputStream newInstance(byte[] buf, int off, int len) {
        return CodedInputStream.newInstance(buf, off, len, false);
    }

    static CodedInputStream newInstance(byte[] buf, int off, int len, boolean bufferIsImmutable) {
        ArrayDecoder result = new ArrayDecoder(buf, off, len, bufferIsImmutable);
        try {
            result.pushLimit(len);
        }
        catch (InvalidProtocolBufferException ex) {
            throw new IllegalArgumentException(ex);
        }
        return result;
    }

    public static CodedInputStream newInstance(ByteBuffer buf) {
        return CodedInputStream.newInstance(buf, false);
    }

    static CodedInputStream newInstance(ByteBuffer buf, boolean bufferIsImmutable) {
        if (buf.hasArray()) {
            return CodedInputStream.newInstance(buf.array(), buf.arrayOffset() + buf.position(), buf.remaining(), bufferIsImmutable);
        }
        if (buf.isDirect() && UnsafeDirectNioDecoder.isSupported()) {
            return new UnsafeDirectNioDecoder(buf, bufferIsImmutable);
        }
        byte[] buffer = new byte[buf.remaining()];
        buf.duplicate().get(buffer);
        return CodedInputStream.newInstance(buffer, 0, buffer.length, true);
    }

    private CodedInputStream() {
    }

    public abstract int readTag() throws IOException;

    public abstract void checkLastTagWas(int var1) throws InvalidProtocolBufferException;

    public abstract int getLastTag();

    public abstract boolean skipField(int var1) throws IOException;

    @Deprecated
    public abstract boolean skipField(int var1, CodedOutputStream var2) throws IOException;

    public abstract void skipMessage() throws IOException;

    public abstract void skipMessage(CodedOutputStream var1) throws IOException;

    public abstract double readDouble() throws IOException;

    public abstract float readFloat() throws IOException;

    public abstract long readUInt64() throws IOException;

    public abstract long readInt64() throws IOException;

    public abstract int readInt32() throws IOException;

    public abstract long readFixed64() throws IOException;

    public abstract int readFixed32() throws IOException;

    public abstract boolean readBool() throws IOException;

    public abstract String readString() throws IOException;

    public abstract String readStringRequireUtf8() throws IOException;

    public abstract void readGroup(int var1, MessageLite.Builder var2, ExtensionRegistryLite var3) throws IOException;

    public abstract <T extends MessageLite> T readGroup(int var1, Parser<T> var2, ExtensionRegistryLite var3) throws IOException;

    @Deprecated
    public abstract void readUnknownGroup(int var1, MessageLite.Builder var2) throws IOException;

    public abstract void readMessage(MessageLite.Builder var1, ExtensionRegistryLite var2) throws IOException;

    public abstract <T extends MessageLite> T readMessage(Parser<T> var1, ExtensionRegistryLite var2) throws IOException;

    public abstract ByteString readBytes() throws IOException;

    public abstract byte[] readByteArray() throws IOException;

    public abstract ByteBuffer readByteBuffer() throws IOException;

    public abstract int readUInt32() throws IOException;

    public abstract int readEnum() throws IOException;

    public abstract int readSFixed32() throws IOException;

    public abstract long readSFixed64() throws IOException;

    public abstract int readSInt32() throws IOException;

    public abstract long readSInt64() throws IOException;

    public abstract int readRawVarint32() throws IOException;

    public abstract long readRawVarint64() throws IOException;

    abstract long readRawVarint64SlowPath() throws IOException;

    public abstract int readRawLittleEndian32() throws IOException;

    public abstract long readRawLittleEndian64() throws IOException;

    public abstract void enableAliasing(boolean var1);

    public final int setRecursionLimit(int limit) {
        if (limit < 0) {
            throw new IllegalArgumentException("Recursion limit cannot be negative: " + limit);
        }
        int oldLimit = this.recursionLimit;
        this.recursionLimit = limit;
        return oldLimit;
    }

    public final int setSizeLimit(int limit) {
        if (limit < 0) {
            throw new IllegalArgumentException("Size limit cannot be negative: " + limit);
        }
        int oldLimit = this.sizeLimit;
        this.sizeLimit = limit;
        return oldLimit;
    }

    static void setProto3DiscardUnknownsByDefaultForTest() {
        proto3DiscardUnknownFieldsDefault = true;
    }

    static void setProto3KeepUnknownsByDefaultForTest() {
        proto3DiscardUnknownFieldsDefault = false;
    }

    static boolean getProto3DiscardUnknownFieldsDefault() {
        return proto3DiscardUnknownFieldsDefault;
    }

    final void discardUnknownFields() {
        this.explicitDiscardUnknownFields = true;
    }

    final void unsetDiscardUnknownFields() {
        this.explicitDiscardUnknownFields = false;
    }

    final boolean shouldDiscardUnknownFields() {
        return this.explicitDiscardUnknownFields;
    }

    final boolean shouldDiscardUnknownFieldsProto3() {
        return this.explicitDiscardUnknownFields ? true : proto3DiscardUnknownFieldsDefault;
    }

    public abstract void resetSizeCounter();

    public abstract int pushLimit(int var1) throws InvalidProtocolBufferException;

    public abstract void popLimit(int var1);

    public abstract int getBytesUntilLimit();

    public abstract boolean isAtEnd() throws IOException;

    public abstract int getTotalBytesRead();

    public abstract byte readRawByte() throws IOException;

    public abstract byte[] readRawBytes(int var1) throws IOException;

    public abstract void skipRawBytes(int var1) throws IOException;

    public static int decodeZigZag32(int n) {
        return n >>> 1 ^ -(n & 1);
    }

    public static long decodeZigZag64(long n) {
        return n >>> 1 ^ -(n & 1L);
    }

    public static int readRawVarint32(int firstByte, InputStream input) throws IOException {
        int b;
        int offset;
        if ((firstByte & 0x80) == 0) {
            return firstByte;
        }
        int result = firstByte & 0x7F;
        for (offset = 7; offset < 32; offset += 7) {
            b = input.read();
            if (b == -1) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            result |= (b & 0x7F) << offset;
            if ((b & 0x80) != 0) continue;
            return result;
        }
        while (offset < 64) {
            b = input.read();
            if (b == -1) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            if ((b & 0x80) == 0) {
                return result;
            }
            offset += 7;
        }
        throw InvalidProtocolBufferException.malformedVarint();
    }

    static int readRawVarint32(InputStream input) throws IOException {
        int firstByte = input.read();
        if (firstByte == -1) {
            throw InvalidProtocolBufferException.truncatedMessage();
        }
        return CodedInputStream.readRawVarint32(firstByte, input);
    }

    private static final class StreamDecoder
    extends CodedInputStream {
        private final InputStream input;
        private final byte[] buffer;
        private int bufferSize;
        private int bufferSizeAfterLimit;
        private int pos;
        private int lastTag;
        private int totalBytesRetired;
        private int currentLimit = Integer.MAX_VALUE;
        private RefillCallback refillCallback = null;

        private StreamDecoder(InputStream input, int bufferSize) {
            Internal.checkNotNull(input, "input");
            this.input = input;
            this.buffer = new byte[bufferSize];
            this.bufferSize = 0;
            this.pos = 0;
            this.totalBytesRetired = 0;
        }

        @Override
        public int readTag() throws IOException {
            if (this.isAtEnd()) {
                this.lastTag = 0;
                return 0;
            }
            this.lastTag = this.readRawVarint32();
            if (WireFormat.getTagFieldNumber(this.lastTag) == 0) {
                throw InvalidProtocolBufferException.invalidTag();
            }
            return this.lastTag;
        }

        @Override
        public void checkLastTagWas(int value) throws InvalidProtocolBufferException {
            if (this.lastTag != value) {
                throw InvalidProtocolBufferException.invalidEndTag();
            }
        }

        @Override
        public int getLastTag() {
            return this.lastTag;
        }

        @Override
        public boolean skipField(int tag) throws IOException {
            switch (WireFormat.getTagWireType(tag)) {
                case 0: {
                    this.skipRawVarint();
                    return true;
                }
                case 1: {
                    this.skipRawBytes(8);
                    return true;
                }
                case 2: {
                    this.skipRawBytes(this.readRawVarint32());
                    return true;
                }
                case 3: {
                    this.skipMessage();
                    this.checkLastTagWas(WireFormat.makeTag(WireFormat.getTagFieldNumber(tag), 4));
                    return true;
                }
                case 4: {
                    return false;
                }
                case 5: {
                    this.skipRawBytes(4);
                    return true;
                }
            }
            throw InvalidProtocolBufferException.invalidWireType();
        }

        @Override
        public boolean skipField(int tag, CodedOutputStream output) throws IOException {
            switch (WireFormat.getTagWireType(tag)) {
                case 0: {
                    long value = this.readInt64();
                    output.writeRawVarint32(tag);
                    output.writeUInt64NoTag(value);
                    return true;
                }
                case 1: {
                    long value = this.readRawLittleEndian64();
                    output.writeRawVarint32(tag);
                    output.writeFixed64NoTag(value);
                    return true;
                }
                case 2: {
                    ByteString value = this.readBytes();
                    output.writeRawVarint32(tag);
                    output.writeBytesNoTag(value);
                    return true;
                }
                case 3: {
                    output.writeRawVarint32(tag);
                    this.skipMessage(output);
                    int endtag = WireFormat.makeTag(WireFormat.getTagFieldNumber(tag), 4);
                    this.checkLastTagWas(endtag);
                    output.writeRawVarint32(endtag);
                    return true;
                }
                case 4: {
                    return false;
                }
                case 5: {
                    int value = this.readRawLittleEndian32();
                    output.writeRawVarint32(tag);
                    output.writeFixed32NoTag(value);
                    return true;
                }
            }
            throw InvalidProtocolBufferException.invalidWireType();
        }

        @Override
        public void skipMessage() throws IOException {
            int tag;
            while ((tag = this.readTag()) != 0 && this.skipField(tag)) {
            }
        }

        @Override
        public void skipMessage(CodedOutputStream output) throws IOException {
            int tag;
            while ((tag = this.readTag()) != 0 && this.skipField(tag, output)) {
            }
        }

        @Override
        public double readDouble() throws IOException {
            return Double.longBitsToDouble(this.readRawLittleEndian64());
        }

        @Override
        public float readFloat() throws IOException {
            return Float.intBitsToFloat(this.readRawLittleEndian32());
        }

        @Override
        public long readUInt64() throws IOException {
            return this.readRawVarint64();
        }

        @Override
        public long readInt64() throws IOException {
            return this.readRawVarint64();
        }

        @Override
        public int readInt32() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public long readFixed64() throws IOException {
            return this.readRawLittleEndian64();
        }

        @Override
        public int readFixed32() throws IOException {
            return this.readRawLittleEndian32();
        }

        @Override
        public boolean readBool() throws IOException {
            return this.readRawVarint64() != 0L;
        }

        @Override
        public String readString() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.bufferSize - this.pos) {
                String result = new String(this.buffer, this.pos, size, Internal.UTF_8);
                this.pos += size;
                return result;
            }
            if (size == 0) {
                return "";
            }
            if (size <= this.bufferSize) {
                this.refillBuffer(size);
                String result = new String(this.buffer, this.pos, size, Internal.UTF_8);
                this.pos += size;
                return result;
            }
            return new String(this.readRawBytesSlowPath(size), Internal.UTF_8);
        }

        @Override
        public String readStringRequireUtf8() throws IOException {
            int tempPos;
            byte[] bytes;
            int oldPos;
            int size = this.readRawVarint32();
            if (size <= this.bufferSize - (oldPos = this.pos) && size > 0) {
                bytes = this.buffer;
                this.pos = oldPos + size;
                tempPos = oldPos;
            } else {
                if (size == 0) {
                    return "";
                }
                if (size <= this.bufferSize) {
                    this.refillBuffer(size);
                    bytes = this.buffer;
                    tempPos = 0;
                    this.pos = tempPos + size;
                } else {
                    bytes = this.readRawBytesSlowPath(size);
                    tempPos = 0;
                }
            }
            if (!Utf8.isValidUtf8(bytes, tempPos, tempPos + size)) {
                throw InvalidProtocolBufferException.invalidUtf8();
            }
            return new String(bytes, tempPos, size, Internal.UTF_8);
        }

        @Override
        public void readGroup(int fieldNumber, MessageLite.Builder builder, ExtensionRegistryLite extensionRegistry) throws IOException {
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            ++this.recursionDepth;
            builder.mergeFrom(this, extensionRegistry);
            this.checkLastTagWas(WireFormat.makeTag(fieldNumber, 4));
            --this.recursionDepth;
        }

        @Override
        public <T extends MessageLite> T readGroup(int fieldNumber, Parser<T> parser, ExtensionRegistryLite extensionRegistry) throws IOException {
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            ++this.recursionDepth;
            MessageLite result = (MessageLite)parser.parsePartialFrom(this, extensionRegistry);
            this.checkLastTagWas(WireFormat.makeTag(fieldNumber, 4));
            --this.recursionDepth;
            return (T)result;
        }

        @Override
        @Deprecated
        public void readUnknownGroup(int fieldNumber, MessageLite.Builder builder) throws IOException {
            this.readGroup(fieldNumber, builder, ExtensionRegistryLite.getEmptyRegistry());
        }

        @Override
        public void readMessage(MessageLite.Builder builder, ExtensionRegistryLite extensionRegistry) throws IOException {
            int length = this.readRawVarint32();
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            int oldLimit = this.pushLimit(length);
            ++this.recursionDepth;
            builder.mergeFrom(this, extensionRegistry);
            this.checkLastTagWas(0);
            --this.recursionDepth;
            this.popLimit(oldLimit);
        }

        @Override
        public <T extends MessageLite> T readMessage(Parser<T> parser, ExtensionRegistryLite extensionRegistry) throws IOException {
            int length = this.readRawVarint32();
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            int oldLimit = this.pushLimit(length);
            ++this.recursionDepth;
            MessageLite result = (MessageLite)parser.parsePartialFrom(this, extensionRegistry);
            this.checkLastTagWas(0);
            --this.recursionDepth;
            this.popLimit(oldLimit);
            return (T)result;
        }

        @Override
        public ByteString readBytes() throws IOException {
            int size = this.readRawVarint32();
            if (size <= this.bufferSize - this.pos && size > 0) {
                ByteString result = ByteString.copyFrom(this.buffer, this.pos, size);
                this.pos += size;
                return result;
            }
            if (size == 0) {
                return ByteString.EMPTY;
            }
            return this.readBytesSlowPath(size);
        }

        @Override
        public byte[] readByteArray() throws IOException {
            int size = this.readRawVarint32();
            if (size <= this.bufferSize - this.pos && size > 0) {
                byte[] result = Arrays.copyOfRange(this.buffer, this.pos, this.pos + size);
                this.pos += size;
                return result;
            }
            return this.readRawBytesSlowPath(size);
        }

        @Override
        public ByteBuffer readByteBuffer() throws IOException {
            int size = this.readRawVarint32();
            if (size <= this.bufferSize - this.pos && size > 0) {
                ByteBuffer result = ByteBuffer.wrap(Arrays.copyOfRange(this.buffer, this.pos, this.pos + size));
                this.pos += size;
                return result;
            }
            if (size == 0) {
                return Internal.EMPTY_BYTE_BUFFER;
            }
            return ByteBuffer.wrap(this.readRawBytesSlowPath(size));
        }

        @Override
        public int readUInt32() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public int readEnum() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public int readSFixed32() throws IOException {
            return this.readRawLittleEndian32();
        }

        @Override
        public long readSFixed64() throws IOException {
            return this.readRawLittleEndian64();
        }

        @Override
        public int readSInt32() throws IOException {
            return StreamDecoder.decodeZigZag32(this.readRawVarint32());
        }

        @Override
        public long readSInt64() throws IOException {
            return StreamDecoder.decodeZigZag64(this.readRawVarint64());
        }

        @Override
        public int readRawVarint32() throws IOException {
            block4: {
                int x;
                int tempPos;
                block6: {
                    byte[] buffer;
                    block8: {
                        block7: {
                            block5: {
                                tempPos = this.pos;
                                if (this.bufferSize == tempPos) break block4;
                                buffer = this.buffer;
                                if ((x = buffer[tempPos++]) >= 0) {
                                    this.pos = tempPos;
                                    return x;
                                }
                                if (this.bufferSize - tempPos < 9) break block4;
                                if ((x ^= buffer[tempPos++] << 7) >= 0) break block5;
                                x ^= 0xFFFFFF80;
                                break block6;
                            }
                            if ((x ^= buffer[tempPos++] << 14) < 0) break block7;
                            x ^= 0x3F80;
                            break block6;
                        }
                        if ((x ^= buffer[tempPos++] << 21) >= 0) break block8;
                        x ^= 0xFFE03F80;
                        break block6;
                    }
                    byte y = buffer[tempPos++];
                    x ^= y << 28;
                    x ^= 0xFE03F80;
                    if (y < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0) break block4;
                }
                this.pos = tempPos;
                return x;
            }
            return (int)this.readRawVarint64SlowPath();
        }

        private void skipRawVarint() throws IOException {
            if (this.bufferSize - this.pos >= 10) {
                this.skipRawVarintFastPath();
            } else {
                this.skipRawVarintSlowPath();
            }
        }

        private void skipRawVarintFastPath() throws IOException {
            for (int i = 0; i < 10; ++i) {
                if (this.buffer[this.pos++] < 0) continue;
                return;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        private void skipRawVarintSlowPath() throws IOException {
            for (int i = 0; i < 10; ++i) {
                if (this.readRawByte() < 0) continue;
                return;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        @Override
        public long readRawVarint64() throws IOException {
            block4: {
                long x;
                int tempPos;
                block6: {
                    byte[] buffer;
                    block12: {
                        block11: {
                            block10: {
                                block9: {
                                    int y;
                                    block8: {
                                        block7: {
                                            block5: {
                                                tempPos = this.pos;
                                                if (this.bufferSize == tempPos) break block4;
                                                buffer = this.buffer;
                                                if ((y = buffer[tempPos++]) >= 0) {
                                                    this.pos = tempPos;
                                                    return y;
                                                }
                                                if (this.bufferSize - tempPos < 9) break block4;
                                                if ((y ^= buffer[tempPos++] << 7) >= 0) break block5;
                                                x = y ^ 0xFFFFFF80;
                                                break block6;
                                            }
                                            if ((y ^= buffer[tempPos++] << 14) < 0) break block7;
                                            x = y ^ 0x3F80;
                                            break block6;
                                        }
                                        if ((y ^= buffer[tempPos++] << 21) >= 0) break block8;
                                        x = y ^ 0xFFE03F80;
                                        break block6;
                                    }
                                    if ((x = (long)y ^ (long)buffer[tempPos++] << 28) < 0L) break block9;
                                    x ^= 0xFE03F80L;
                                    break block6;
                                }
                                if ((x ^= (long)buffer[tempPos++] << 35) >= 0L) break block10;
                                x ^= 0xFFFFFFF80FE03F80L;
                                break block6;
                            }
                            if ((x ^= (long)buffer[tempPos++] << 42) < 0L) break block11;
                            x ^= 0x3F80FE03F80L;
                            break block6;
                        }
                        if ((x ^= (long)buffer[tempPos++] << 49) >= 0L) break block12;
                        x ^= 0xFFFE03F80FE03F80L;
                        break block6;
                    }
                    x ^= (long)buffer[tempPos++] << 56;
                    if ((x ^= 0xFE03F80FE03F80L) < 0L && (long)buffer[tempPos++] < 0L) break block4;
                }
                this.pos = tempPos;
                return x;
            }
            return this.readRawVarint64SlowPath();
        }

        @Override
        long readRawVarint64SlowPath() throws IOException {
            long result = 0L;
            for (int shift = 0; shift < 64; shift += 7) {
                byte b = this.readRawByte();
                result |= (long)(b & 0x7F) << shift;
                if ((b & 0x80) != 0) continue;
                return result;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        @Override
        public int readRawLittleEndian32() throws IOException {
            int tempPos = this.pos;
            if (this.bufferSize - tempPos < 4) {
                this.refillBuffer(4);
                tempPos = this.pos;
            }
            byte[] buffer = this.buffer;
            this.pos = tempPos + 4;
            return buffer[tempPos] & 0xFF | (buffer[tempPos + 1] & 0xFF) << 8 | (buffer[tempPos + 2] & 0xFF) << 16 | (buffer[tempPos + 3] & 0xFF) << 24;
        }

        @Override
        public long readRawLittleEndian64() throws IOException {
            int tempPos = this.pos;
            if (this.bufferSize - tempPos < 8) {
                this.refillBuffer(8);
                tempPos = this.pos;
            }
            byte[] buffer = this.buffer;
            this.pos = tempPos + 8;
            return (long)buffer[tempPos] & 0xFFL | ((long)buffer[tempPos + 1] & 0xFFL) << 8 | ((long)buffer[tempPos + 2] & 0xFFL) << 16 | ((long)buffer[tempPos + 3] & 0xFFL) << 24 | ((long)buffer[tempPos + 4] & 0xFFL) << 32 | ((long)buffer[tempPos + 5] & 0xFFL) << 40 | ((long)buffer[tempPos + 6] & 0xFFL) << 48 | ((long)buffer[tempPos + 7] & 0xFFL) << 56;
        }

        @Override
        public void enableAliasing(boolean enabled) {
        }

        @Override
        public void resetSizeCounter() {
            this.totalBytesRetired = -this.pos;
        }

        @Override
        public int pushLimit(int byteLimit) throws InvalidProtocolBufferException {
            if (byteLimit < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            int oldLimit = this.currentLimit;
            if ((byteLimit += this.totalBytesRetired + this.pos) > oldLimit) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            this.currentLimit = byteLimit;
            this.recomputeBufferSizeAfterLimit();
            return oldLimit;
        }

        private void recomputeBufferSizeAfterLimit() {
            this.bufferSize += this.bufferSizeAfterLimit;
            int bufferEnd = this.totalBytesRetired + this.bufferSize;
            if (bufferEnd > this.currentLimit) {
                this.bufferSizeAfterLimit = bufferEnd - this.currentLimit;
                this.bufferSize -= this.bufferSizeAfterLimit;
            } else {
                this.bufferSizeAfterLimit = 0;
            }
        }

        @Override
        public void popLimit(int oldLimit) {
            this.currentLimit = oldLimit;
            this.recomputeBufferSizeAfterLimit();
        }

        @Override
        public int getBytesUntilLimit() {
            if (this.currentLimit == Integer.MAX_VALUE) {
                return -1;
            }
            int currentAbsolutePosition = this.totalBytesRetired + this.pos;
            return this.currentLimit - currentAbsolutePosition;
        }

        @Override
        public boolean isAtEnd() throws IOException {
            return this.pos == this.bufferSize && !this.tryRefillBuffer(1);
        }

        @Override
        public int getTotalBytesRead() {
            return this.totalBytesRetired + this.pos;
        }

        private void refillBuffer(int n) throws IOException {
            if (!this.tryRefillBuffer(n)) {
                if (n > this.sizeLimit - this.totalBytesRetired - this.pos) {
                    throw InvalidProtocolBufferException.sizeLimitExceeded();
                }
                throw InvalidProtocolBufferException.truncatedMessage();
            }
        }

        private boolean tryRefillBuffer(int n) throws IOException {
            int bytesRead;
            int tempPos;
            if (this.pos + n <= this.bufferSize) {
                throw new IllegalStateException("refillBuffer() called when " + n + " bytes were already available in buffer");
            }
            if (n > this.sizeLimit - this.totalBytesRetired - this.pos) {
                return false;
            }
            if (this.totalBytesRetired + this.pos + n > this.currentLimit) {
                return false;
            }
            if (this.refillCallback != null) {
                this.refillCallback.onRefill();
            }
            if ((tempPos = this.pos) > 0) {
                if (this.bufferSize > tempPos) {
                    System.arraycopy(this.buffer, tempPos, this.buffer, 0, this.bufferSize - tempPos);
                }
                this.totalBytesRetired += tempPos;
                this.bufferSize -= tempPos;
                this.pos = 0;
            }
            if ((bytesRead = this.input.read(this.buffer, this.bufferSize, Math.min(this.buffer.length - this.bufferSize, this.sizeLimit - this.totalBytesRetired - this.bufferSize))) == 0 || bytesRead < -1 || bytesRead > this.buffer.length) {
                throw new IllegalStateException("InputStream#read(byte[]) returned invalid result: " + bytesRead + "\nThe InputStream implementation is buggy.");
            }
            if (bytesRead > 0) {
                this.bufferSize += bytesRead;
                this.recomputeBufferSizeAfterLimit();
                return this.bufferSize >= n ? true : this.tryRefillBuffer(n);
            }
            return false;
        }

        @Override
        public byte readRawByte() throws IOException {
            if (this.pos == this.bufferSize) {
                this.refillBuffer(1);
            }
            return this.buffer[this.pos++];
        }

        @Override
        public byte[] readRawBytes(int size) throws IOException {
            int tempPos = this.pos;
            if (size <= this.bufferSize - tempPos && size > 0) {
                this.pos = tempPos + size;
                return Arrays.copyOfRange(this.buffer, tempPos, tempPos + size);
            }
            return this.readRawBytesSlowPath(size);
        }

        private byte[] readRawBytesSlowPath(int size) throws IOException {
            byte[] result = this.readRawBytesSlowPathOneChunk(size);
            if (result != null) {
                return result;
            }
            int originalBufferPos = this.pos;
            int bufferedBytes = this.bufferSize - this.pos;
            this.totalBytesRetired += this.bufferSize;
            this.pos = 0;
            this.bufferSize = 0;
            int sizeLeft = size - bufferedBytes;
            List<byte[]> chunks = this.readRawBytesSlowPathRemainingChunks(sizeLeft);
            byte[] bytes = new byte[size];
            System.arraycopy(this.buffer, originalBufferPos, bytes, 0, bufferedBytes);
            int tempPos = bufferedBytes;
            for (byte[] chunk : chunks) {
                System.arraycopy(chunk, 0, bytes, tempPos, chunk.length);
                tempPos += chunk.length;
            }
            return bytes;
        }

        private byte[] readRawBytesSlowPathOneChunk(int size) throws IOException {
            if (size == 0) {
                return Internal.EMPTY_BYTE_ARRAY;
            }
            if (size < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            int currentMessageSize = this.totalBytesRetired + this.pos + size;
            if (currentMessageSize - this.sizeLimit > 0) {
                throw InvalidProtocolBufferException.sizeLimitExceeded();
            }
            if (currentMessageSize > this.currentLimit) {
                this.skipRawBytes(this.currentLimit - this.totalBytesRetired - this.pos);
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            int bufferedBytes = this.bufferSize - this.pos;
            int sizeLeft = size - bufferedBytes;
            if (sizeLeft < 4096 || sizeLeft <= this.input.available()) {
                int n;
                byte[] bytes = new byte[size];
                System.arraycopy(this.buffer, this.pos, bytes, 0, bufferedBytes);
                this.totalBytesRetired += this.bufferSize;
                this.pos = 0;
                this.bufferSize = 0;
                for (int tempPos = bufferedBytes; tempPos < bytes.length; tempPos += n) {
                    n = this.input.read(bytes, tempPos, size - tempPos);
                    if (n == -1) {
                        throw InvalidProtocolBufferException.truncatedMessage();
                    }
                    this.totalBytesRetired += n;
                }
                return bytes;
            }
            return null;
        }

        private List<byte[]> readRawBytesSlowPathRemainingChunks(int sizeLeft) throws IOException {
            ArrayList<byte[]> chunks = new ArrayList<byte[]>();
            while (sizeLeft > 0) {
                int n;
                byte[] chunk = new byte[Math.min(sizeLeft, 4096)];
                for (int tempPos = 0; tempPos < chunk.length; tempPos += n) {
                    n = this.input.read(chunk, tempPos, chunk.length - tempPos);
                    if (n == -1) {
                        throw InvalidProtocolBufferException.truncatedMessage();
                    }
                    this.totalBytesRetired += n;
                }
                sizeLeft -= chunk.length;
                chunks.add(chunk);
            }
            return chunks;
        }

        private ByteString readBytesSlowPath(int size) throws IOException {
            byte[] result = this.readRawBytesSlowPathOneChunk(size);
            if (result != null) {
                return ByteString.wrap(result);
            }
            int originalBufferPos = this.pos;
            int bufferedBytes = this.bufferSize - this.pos;
            this.totalBytesRetired += this.bufferSize;
            this.pos = 0;
            this.bufferSize = 0;
            int sizeLeft = size - bufferedBytes;
            List<byte[]> chunks = this.readRawBytesSlowPathRemainingChunks(sizeLeft);
            ArrayList<ByteString> byteStrings = new ArrayList<ByteString>(1 + chunks.size());
            byteStrings.add(ByteString.copyFrom(this.buffer, originalBufferPos, bufferedBytes));
            for (byte[] chunk : chunks) {
                byteStrings.add(ByteString.wrap(chunk));
            }
            return ByteString.copyFrom(byteStrings);
        }

        @Override
        public void skipRawBytes(int size) throws IOException {
            if (size <= this.bufferSize - this.pos && size >= 0) {
                this.pos += size;
            } else {
                this.skipRawBytesSlowPath(size);
            }
        }

        private void skipRawBytesSlowPath(int size) throws IOException {
            if (size < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            if (this.totalBytesRetired + this.pos + size > this.currentLimit) {
                this.skipRawBytes(this.currentLimit - this.totalBytesRetired - this.pos);
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            int tempPos = this.bufferSize - this.pos;
            this.pos = this.bufferSize;
            this.refillBuffer(1);
            while (size - tempPos > this.bufferSize) {
                tempPos += this.bufferSize;
                this.pos = this.bufferSize;
                this.refillBuffer(1);
            }
            this.pos = size - tempPos;
        }

        private static interface RefillCallback {
            public void onRefill();
        }

        private class SkippedDataSink
        implements RefillCallback {
            private int lastPos;
            private ByteArrayOutputStream byteArrayStream;

            private SkippedDataSink() {
                this.lastPos = StreamDecoder.this.pos;
            }

            @Override
            public void onRefill() {
                if (this.byteArrayStream == null) {
                    this.byteArrayStream = new ByteArrayOutputStream();
                }
                this.byteArrayStream.write(StreamDecoder.this.buffer, this.lastPos, StreamDecoder.this.pos - this.lastPos);
                this.lastPos = 0;
            }

            ByteBuffer getSkippedData() {
                if (this.byteArrayStream == null) {
                    return ByteBuffer.wrap(StreamDecoder.this.buffer, this.lastPos, StreamDecoder.this.pos - this.lastPos);
                }
                this.byteArrayStream.write(StreamDecoder.this.buffer, this.lastPos, StreamDecoder.this.pos);
                return ByteBuffer.wrap(this.byteArrayStream.toByteArray());
            }
        }
    }

    private static final class UnsafeDirectNioDecoder
    extends CodedInputStream {
        private final ByteBuffer buffer;
        private final boolean immutable;
        private final long address;
        private long limit;
        private long pos;
        private long startPos;
        private int bufferSizeAfterLimit;
        private int lastTag;
        private boolean enableAliasing;
        private int currentLimit = Integer.MAX_VALUE;

        static boolean isSupported() {
            return UnsafeUtil.hasUnsafeByteBufferOperations();
        }

        private UnsafeDirectNioDecoder(ByteBuffer buffer, boolean immutable) {
            this.buffer = buffer;
            this.address = UnsafeUtil.addressOffset(buffer);
            this.limit = this.address + (long)buffer.limit();
            this.startPos = this.pos = this.address + (long)buffer.position();
            this.immutable = immutable;
        }

        @Override
        public int readTag() throws IOException {
            if (this.isAtEnd()) {
                this.lastTag = 0;
                return 0;
            }
            this.lastTag = this.readRawVarint32();
            if (WireFormat.getTagFieldNumber(this.lastTag) == 0) {
                throw InvalidProtocolBufferException.invalidTag();
            }
            return this.lastTag;
        }

        @Override
        public void checkLastTagWas(int value) throws InvalidProtocolBufferException {
            if (this.lastTag != value) {
                throw InvalidProtocolBufferException.invalidEndTag();
            }
        }

        @Override
        public int getLastTag() {
            return this.lastTag;
        }

        @Override
        public boolean skipField(int tag) throws IOException {
            switch (WireFormat.getTagWireType(tag)) {
                case 0: {
                    this.skipRawVarint();
                    return true;
                }
                case 1: {
                    this.skipRawBytes(8);
                    return true;
                }
                case 2: {
                    this.skipRawBytes(this.readRawVarint32());
                    return true;
                }
                case 3: {
                    this.skipMessage();
                    this.checkLastTagWas(WireFormat.makeTag(WireFormat.getTagFieldNumber(tag), 4));
                    return true;
                }
                case 4: {
                    return false;
                }
                case 5: {
                    this.skipRawBytes(4);
                    return true;
                }
            }
            throw InvalidProtocolBufferException.invalidWireType();
        }

        @Override
        public boolean skipField(int tag, CodedOutputStream output) throws IOException {
            switch (WireFormat.getTagWireType(tag)) {
                case 0: {
                    long value = this.readInt64();
                    output.writeRawVarint32(tag);
                    output.writeUInt64NoTag(value);
                    return true;
                }
                case 1: {
                    long value = this.readRawLittleEndian64();
                    output.writeRawVarint32(tag);
                    output.writeFixed64NoTag(value);
                    return true;
                }
                case 2: {
                    ByteString value = this.readBytes();
                    output.writeRawVarint32(tag);
                    output.writeBytesNoTag(value);
                    return true;
                }
                case 3: {
                    output.writeRawVarint32(tag);
                    this.skipMessage(output);
                    int endtag = WireFormat.makeTag(WireFormat.getTagFieldNumber(tag), 4);
                    this.checkLastTagWas(endtag);
                    output.writeRawVarint32(endtag);
                    return true;
                }
                case 4: {
                    return false;
                }
                case 5: {
                    int value = this.readRawLittleEndian32();
                    output.writeRawVarint32(tag);
                    output.writeFixed32NoTag(value);
                    return true;
                }
            }
            throw InvalidProtocolBufferException.invalidWireType();
        }

        @Override
        public void skipMessage() throws IOException {
            int tag;
            while ((tag = this.readTag()) != 0 && this.skipField(tag)) {
            }
        }

        @Override
        public void skipMessage(CodedOutputStream output) throws IOException {
            int tag;
            while ((tag = this.readTag()) != 0 && this.skipField(tag, output)) {
            }
        }

        @Override
        public double readDouble() throws IOException {
            return Double.longBitsToDouble(this.readRawLittleEndian64());
        }

        @Override
        public float readFloat() throws IOException {
            return Float.intBitsToFloat(this.readRawLittleEndian32());
        }

        @Override
        public long readUInt64() throws IOException {
            return this.readRawVarint64();
        }

        @Override
        public long readInt64() throws IOException {
            return this.readRawVarint64();
        }

        @Override
        public int readInt32() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public long readFixed64() throws IOException {
            return this.readRawLittleEndian64();
        }

        @Override
        public int readFixed32() throws IOException {
            return this.readRawLittleEndian32();
        }

        @Override
        public boolean readBool() throws IOException {
            return this.readRawVarint64() != 0L;
        }

        @Override
        public String readString() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.remaining()) {
                byte[] bytes = new byte[size];
                UnsafeUtil.copyMemory(this.pos, bytes, 0L, (long)size);
                String result = new String(bytes, Internal.UTF_8);
                this.pos += (long)size;
                return result;
            }
            if (size == 0) {
                return "";
            }
            if (size < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public String readStringRequireUtf8() throws IOException {
            int size = this.readRawVarint32();
            if (size >= 0 && size <= this.remaining()) {
                byte[] bytes = new byte[size];
                UnsafeUtil.copyMemory(this.pos, bytes, 0L, (long)size);
                if (!Utf8.isValidUtf8(bytes)) {
                    throw InvalidProtocolBufferException.invalidUtf8();
                }
                String result = new String(bytes, Internal.UTF_8);
                this.pos += (long)size;
                return result;
            }
            if (size == 0) {
                return "";
            }
            if (size <= 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public void readGroup(int fieldNumber, MessageLite.Builder builder, ExtensionRegistryLite extensionRegistry) throws IOException {
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            ++this.recursionDepth;
            builder.mergeFrom(this, extensionRegistry);
            this.checkLastTagWas(WireFormat.makeTag(fieldNumber, 4));
            --this.recursionDepth;
        }

        @Override
        public <T extends MessageLite> T readGroup(int fieldNumber, Parser<T> parser, ExtensionRegistryLite extensionRegistry) throws IOException {
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            ++this.recursionDepth;
            MessageLite result = (MessageLite)parser.parsePartialFrom(this, extensionRegistry);
            this.checkLastTagWas(WireFormat.makeTag(fieldNumber, 4));
            --this.recursionDepth;
            return (T)result;
        }

        @Override
        @Deprecated
        public void readUnknownGroup(int fieldNumber, MessageLite.Builder builder) throws IOException {
            this.readGroup(fieldNumber, builder, ExtensionRegistryLite.getEmptyRegistry());
        }

        @Override
        public void readMessage(MessageLite.Builder builder, ExtensionRegistryLite extensionRegistry) throws IOException {
            int length = this.readRawVarint32();
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            int oldLimit = this.pushLimit(length);
            ++this.recursionDepth;
            builder.mergeFrom(this, extensionRegistry);
            this.checkLastTagWas(0);
            --this.recursionDepth;
            this.popLimit(oldLimit);
        }

        @Override
        public <T extends MessageLite> T readMessage(Parser<T> parser, ExtensionRegistryLite extensionRegistry) throws IOException {
            int length = this.readRawVarint32();
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            int oldLimit = this.pushLimit(length);
            ++this.recursionDepth;
            MessageLite result = (MessageLite)parser.parsePartialFrom(this, extensionRegistry);
            this.checkLastTagWas(0);
            --this.recursionDepth;
            this.popLimit(oldLimit);
            return (T)result;
        }

        @Override
        public ByteString readBytes() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.remaining()) {
                if (this.immutable && this.enableAliasing) {
                    ByteBuffer result = this.slice(this.pos, this.pos + (long)size);
                    this.pos += (long)size;
                    return ByteString.wrap(result);
                }
                byte[] bytes = new byte[size];
                UnsafeUtil.copyMemory(this.pos, bytes, 0L, (long)size);
                this.pos += (long)size;
                return ByteString.wrap(bytes);
            }
            if (size == 0) {
                return ByteString.EMPTY;
            }
            if (size < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public byte[] readByteArray() throws IOException {
            return this.readRawBytes(this.readRawVarint32());
        }

        @Override
        public ByteBuffer readByteBuffer() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.remaining()) {
                if (!this.immutable && this.enableAliasing) {
                    ByteBuffer result = this.slice(this.pos, this.pos + (long)size);
                    this.pos += (long)size;
                    return result;
                }
                byte[] bytes = new byte[size];
                UnsafeUtil.copyMemory(this.pos, bytes, 0L, (long)size);
                this.pos += (long)size;
                return ByteBuffer.wrap(bytes);
            }
            if (size == 0) {
                return Internal.EMPTY_BYTE_BUFFER;
            }
            if (size < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public int readUInt32() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public int readEnum() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public int readSFixed32() throws IOException {
            return this.readRawLittleEndian32();
        }

        @Override
        public long readSFixed64() throws IOException {
            return this.readRawLittleEndian64();
        }

        @Override
        public int readSInt32() throws IOException {
            return UnsafeDirectNioDecoder.decodeZigZag32(this.readRawVarint32());
        }

        @Override
        public long readSInt64() throws IOException {
            return UnsafeDirectNioDecoder.decodeZigZag64(this.readRawVarint64());
        }

        @Override
        public int readRawVarint32() throws IOException {
            block4: {
                int x;
                long tempPos;
                block6: {
                    block8: {
                        block7: {
                            block5: {
                                tempPos = this.pos;
                                if (this.limit == tempPos) break block4;
                                if ((x = UnsafeUtil.getByte(tempPos++)) >= 0) {
                                    this.pos = tempPos;
                                    return x;
                                }
                                if (this.limit - tempPos < 9L) break block4;
                                if ((x ^= UnsafeUtil.getByte(tempPos++) << 7) >= 0) break block5;
                                x ^= 0xFFFFFF80;
                                break block6;
                            }
                            if ((x ^= UnsafeUtil.getByte(tempPos++) << 14) < 0) break block7;
                            x ^= 0x3F80;
                            break block6;
                        }
                        if ((x ^= UnsafeUtil.getByte(tempPos++) << 21) >= 0) break block8;
                        x ^= 0xFFE03F80;
                        break block6;
                    }
                    byte y = UnsafeUtil.getByte(tempPos++);
                    x ^= y << 28;
                    x ^= 0xFE03F80;
                    if (y < 0 && UnsafeUtil.getByte(tempPos++) < 0 && UnsafeUtil.getByte(tempPos++) < 0 && UnsafeUtil.getByte(tempPos++) < 0 && UnsafeUtil.getByte(tempPos++) < 0 && UnsafeUtil.getByte(tempPos++) < 0) break block4;
                }
                this.pos = tempPos;
                return x;
            }
            return (int)this.readRawVarint64SlowPath();
        }

        private void skipRawVarint() throws IOException {
            if (this.remaining() >= 10) {
                this.skipRawVarintFastPath();
            } else {
                this.skipRawVarintSlowPath();
            }
        }

        private void skipRawVarintFastPath() throws IOException {
            for (int i = 0; i < 10; ++i) {
                if (UnsafeUtil.getByte(this.pos++) < 0) continue;
                return;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        private void skipRawVarintSlowPath() throws IOException {
            for (int i = 0; i < 10; ++i) {
                if (this.readRawByte() < 0) continue;
                return;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        @Override
        public long readRawVarint64() throws IOException {
            block4: {
                long x;
                long tempPos;
                block6: {
                    block12: {
                        block11: {
                            block10: {
                                block9: {
                                    int y;
                                    block8: {
                                        block7: {
                                            block5: {
                                                tempPos = this.pos;
                                                if (this.limit == tempPos) break block4;
                                                if ((y = UnsafeUtil.getByte(tempPos++)) >= 0) {
                                                    this.pos = tempPos;
                                                    return y;
                                                }
                                                if (this.limit - tempPos < 9L) break block4;
                                                if ((y ^= UnsafeUtil.getByte(tempPos++) << 7) >= 0) break block5;
                                                x = y ^ 0xFFFFFF80;
                                                break block6;
                                            }
                                            if ((y ^= UnsafeUtil.getByte(tempPos++) << 14) < 0) break block7;
                                            x = y ^ 0x3F80;
                                            break block6;
                                        }
                                        if ((y ^= UnsafeUtil.getByte(tempPos++) << 21) >= 0) break block8;
                                        x = y ^ 0xFFE03F80;
                                        break block6;
                                    }
                                    if ((x = (long)y ^ (long)UnsafeUtil.getByte(tempPos++) << 28) < 0L) break block9;
                                    x ^= 0xFE03F80L;
                                    break block6;
                                }
                                if ((x ^= (long)UnsafeUtil.getByte(tempPos++) << 35) >= 0L) break block10;
                                x ^= 0xFFFFFFF80FE03F80L;
                                break block6;
                            }
                            if ((x ^= (long)UnsafeUtil.getByte(tempPos++) << 42) < 0L) break block11;
                            x ^= 0x3F80FE03F80L;
                            break block6;
                        }
                        if ((x ^= (long)UnsafeUtil.getByte(tempPos++) << 49) >= 0L) break block12;
                        x ^= 0xFFFE03F80FE03F80L;
                        break block6;
                    }
                    x ^= (long)UnsafeUtil.getByte(tempPos++) << 56;
                    if ((x ^= 0xFE03F80FE03F80L) < 0L && (long)UnsafeUtil.getByte(tempPos++) < 0L) break block4;
                }
                this.pos = tempPos;
                return x;
            }
            return this.readRawVarint64SlowPath();
        }

        @Override
        long readRawVarint64SlowPath() throws IOException {
            long result = 0L;
            for (int shift = 0; shift < 64; shift += 7) {
                byte b = this.readRawByte();
                result |= (long)(b & 0x7F) << shift;
                if ((b & 0x80) != 0) continue;
                return result;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        @Override
        public int readRawLittleEndian32() throws IOException {
            long tempPos = this.pos;
            if (this.limit - tempPos < 4L) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            this.pos = tempPos + 4L;
            return UnsafeUtil.getByte(tempPos) & 0xFF | (UnsafeUtil.getByte(tempPos + 1L) & 0xFF) << 8 | (UnsafeUtil.getByte(tempPos + 2L) & 0xFF) << 16 | (UnsafeUtil.getByte(tempPos + 3L) & 0xFF) << 24;
        }

        @Override
        public long readRawLittleEndian64() throws IOException {
            long tempPos = this.pos;
            if (this.limit - tempPos < 8L) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            this.pos = tempPos + 8L;
            return (long)UnsafeUtil.getByte(tempPos) & 0xFFL | ((long)UnsafeUtil.getByte(tempPos + 1L) & 0xFFL) << 8 | ((long)UnsafeUtil.getByte(tempPos + 2L) & 0xFFL) << 16 | ((long)UnsafeUtil.getByte(tempPos + 3L) & 0xFFL) << 24 | ((long)UnsafeUtil.getByte(tempPos + 4L) & 0xFFL) << 32 | ((long)UnsafeUtil.getByte(tempPos + 5L) & 0xFFL) << 40 | ((long)UnsafeUtil.getByte(tempPos + 6L) & 0xFFL) << 48 | ((long)UnsafeUtil.getByte(tempPos + 7L) & 0xFFL) << 56;
        }

        @Override
        public void enableAliasing(boolean enabled) {
            this.enableAliasing = enabled;
        }

        @Override
        public void resetSizeCounter() {
            this.startPos = this.pos;
        }

        @Override
        public int pushLimit(int byteLimit) throws InvalidProtocolBufferException {
            int oldLimit;
            if (byteLimit < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            if ((byteLimit += this.getTotalBytesRead()) > (oldLimit = this.currentLimit)) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            this.currentLimit = byteLimit;
            this.recomputeBufferSizeAfterLimit();
            return oldLimit;
        }

        @Override
        public void popLimit(int oldLimit) {
            this.currentLimit = oldLimit;
            this.recomputeBufferSizeAfterLimit();
        }

        @Override
        public int getBytesUntilLimit() {
            if (this.currentLimit == Integer.MAX_VALUE) {
                return -1;
            }
            return this.currentLimit - this.getTotalBytesRead();
        }

        @Override
        public boolean isAtEnd() throws IOException {
            return this.pos == this.limit;
        }

        @Override
        public int getTotalBytesRead() {
            return (int)(this.pos - this.startPos);
        }

        @Override
        public byte readRawByte() throws IOException {
            if (this.pos == this.limit) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            return UnsafeUtil.getByte(this.pos++);
        }

        @Override
        public byte[] readRawBytes(int length) throws IOException {
            if (length >= 0 && length <= this.remaining()) {
                byte[] bytes = new byte[length];
                this.slice(this.pos, this.pos + (long)length).get(bytes);
                this.pos += (long)length;
                return bytes;
            }
            if (length <= 0) {
                if (length == 0) {
                    return Internal.EMPTY_BYTE_ARRAY;
                }
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public void skipRawBytes(int length) throws IOException {
            if (length >= 0 && length <= this.remaining()) {
                this.pos += (long)length;
                return;
            }
            if (length < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        private void recomputeBufferSizeAfterLimit() {
            this.limit += (long)this.bufferSizeAfterLimit;
            int bufferEnd = (int)(this.limit - this.startPos);
            if (bufferEnd > this.currentLimit) {
                this.bufferSizeAfterLimit = bufferEnd - this.currentLimit;
                this.limit -= (long)this.bufferSizeAfterLimit;
            } else {
                this.bufferSizeAfterLimit = 0;
            }
        }

        private int remaining() {
            return (int)(this.limit - this.pos);
        }

        private int bufferPos(long pos) {
            return (int)(pos - this.address);
        }

        private ByteBuffer slice(long begin, long end) throws IOException {
            int prevPos = this.buffer.position();
            int prevLimit = this.buffer.limit();
            try {
                this.buffer.position(this.bufferPos(begin));
                this.buffer.limit(this.bufferPos(end));
                ByteBuffer byteBuffer = this.buffer.slice();
                return byteBuffer;
            }
            catch (IllegalArgumentException e) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            finally {
                this.buffer.position(prevPos);
                this.buffer.limit(prevLimit);
            }
        }
    }

    private static final class ArrayDecoder
    extends CodedInputStream {
        private final byte[] buffer;
        private final boolean immutable;
        private int limit;
        private int bufferSizeAfterLimit;
        private int pos;
        private int startPos;
        private int lastTag;
        private boolean enableAliasing;
        private int currentLimit = Integer.MAX_VALUE;

        private ArrayDecoder(byte[] buffer, int offset, int len, boolean immutable) {
            this.buffer = buffer;
            this.limit = offset + len;
            this.startPos = this.pos = offset;
            this.immutable = immutable;
        }

        @Override
        public int readTag() throws IOException {
            if (this.isAtEnd()) {
                this.lastTag = 0;
                return 0;
            }
            this.lastTag = this.readRawVarint32();
            if (WireFormat.getTagFieldNumber(this.lastTag) == 0) {
                throw InvalidProtocolBufferException.invalidTag();
            }
            return this.lastTag;
        }

        @Override
        public void checkLastTagWas(int value) throws InvalidProtocolBufferException {
            if (this.lastTag != value) {
                throw InvalidProtocolBufferException.invalidEndTag();
            }
        }

        @Override
        public int getLastTag() {
            return this.lastTag;
        }

        @Override
        public boolean skipField(int tag) throws IOException {
            switch (WireFormat.getTagWireType(tag)) {
                case 0: {
                    this.skipRawVarint();
                    return true;
                }
                case 1: {
                    this.skipRawBytes(8);
                    return true;
                }
                case 2: {
                    this.skipRawBytes(this.readRawVarint32());
                    return true;
                }
                case 3: {
                    this.skipMessage();
                    this.checkLastTagWas(WireFormat.makeTag(WireFormat.getTagFieldNumber(tag), 4));
                    return true;
                }
                case 4: {
                    return false;
                }
                case 5: {
                    this.skipRawBytes(4);
                    return true;
                }
            }
            throw InvalidProtocolBufferException.invalidWireType();
        }

        @Override
        public boolean skipField(int tag, CodedOutputStream output) throws IOException {
            switch (WireFormat.getTagWireType(tag)) {
                case 0: {
                    long value = this.readInt64();
                    output.writeRawVarint32(tag);
                    output.writeUInt64NoTag(value);
                    return true;
                }
                case 1: {
                    long value = this.readRawLittleEndian64();
                    output.writeRawVarint32(tag);
                    output.writeFixed64NoTag(value);
                    return true;
                }
                case 2: {
                    ByteString value = this.readBytes();
                    output.writeRawVarint32(tag);
                    output.writeBytesNoTag(value);
                    return true;
                }
                case 3: {
                    output.writeRawVarint32(tag);
                    this.skipMessage(output);
                    int endtag = WireFormat.makeTag(WireFormat.getTagFieldNumber(tag), 4);
                    this.checkLastTagWas(endtag);
                    output.writeRawVarint32(endtag);
                    return true;
                }
                case 4: {
                    return false;
                }
                case 5: {
                    int value = this.readRawLittleEndian32();
                    output.writeRawVarint32(tag);
                    output.writeFixed32NoTag(value);
                    return true;
                }
            }
            throw InvalidProtocolBufferException.invalidWireType();
        }

        @Override
        public void skipMessage() throws IOException {
            int tag;
            while ((tag = this.readTag()) != 0 && this.skipField(tag)) {
            }
        }

        @Override
        public void skipMessage(CodedOutputStream output) throws IOException {
            int tag;
            while ((tag = this.readTag()) != 0 && this.skipField(tag, output)) {
            }
        }

        @Override
        public double readDouble() throws IOException {
            return Double.longBitsToDouble(this.readRawLittleEndian64());
        }

        @Override
        public float readFloat() throws IOException {
            return Float.intBitsToFloat(this.readRawLittleEndian32());
        }

        @Override
        public long readUInt64() throws IOException {
            return this.readRawVarint64();
        }

        @Override
        public long readInt64() throws IOException {
            return this.readRawVarint64();
        }

        @Override
        public int readInt32() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public long readFixed64() throws IOException {
            return this.readRawLittleEndian64();
        }

        @Override
        public int readFixed32() throws IOException {
            return this.readRawLittleEndian32();
        }

        @Override
        public boolean readBool() throws IOException {
            return this.readRawVarint64() != 0L;
        }

        @Override
        public String readString() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.limit - this.pos) {
                String result = new String(this.buffer, this.pos, size, Internal.UTF_8);
                this.pos += size;
                return result;
            }
            if (size == 0) {
                return "";
            }
            if (size < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public String readStringRequireUtf8() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.limit - this.pos) {
                if (!Utf8.isValidUtf8(this.buffer, this.pos, this.pos + size)) {
                    throw InvalidProtocolBufferException.invalidUtf8();
                }
                int tempPos = this.pos;
                this.pos += size;
                return new String(this.buffer, tempPos, size, Internal.UTF_8);
            }
            if (size == 0) {
                return "";
            }
            if (size <= 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public void readGroup(int fieldNumber, MessageLite.Builder builder, ExtensionRegistryLite extensionRegistry) throws IOException {
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            ++this.recursionDepth;
            builder.mergeFrom(this, extensionRegistry);
            this.checkLastTagWas(WireFormat.makeTag(fieldNumber, 4));
            --this.recursionDepth;
        }

        @Override
        public <T extends MessageLite> T readGroup(int fieldNumber, Parser<T> parser, ExtensionRegistryLite extensionRegistry) throws IOException {
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            ++this.recursionDepth;
            MessageLite result = (MessageLite)parser.parsePartialFrom(this, extensionRegistry);
            this.checkLastTagWas(WireFormat.makeTag(fieldNumber, 4));
            --this.recursionDepth;
            return (T)result;
        }

        @Override
        @Deprecated
        public void readUnknownGroup(int fieldNumber, MessageLite.Builder builder) throws IOException {
            this.readGroup(fieldNumber, builder, ExtensionRegistryLite.getEmptyRegistry());
        }

        @Override
        public void readMessage(MessageLite.Builder builder, ExtensionRegistryLite extensionRegistry) throws IOException {
            int length = this.readRawVarint32();
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            int oldLimit = this.pushLimit(length);
            ++this.recursionDepth;
            builder.mergeFrom(this, extensionRegistry);
            this.checkLastTagWas(0);
            --this.recursionDepth;
            this.popLimit(oldLimit);
        }

        @Override
        public <T extends MessageLite> T readMessage(Parser<T> parser, ExtensionRegistryLite extensionRegistry) throws IOException {
            int length = this.readRawVarint32();
            if (this.recursionDepth >= this.recursionLimit) {
                throw InvalidProtocolBufferException.recursionLimitExceeded();
            }
            int oldLimit = this.pushLimit(length);
            ++this.recursionDepth;
            MessageLite result = (MessageLite)parser.parsePartialFrom(this, extensionRegistry);
            this.checkLastTagWas(0);
            --this.recursionDepth;
            this.popLimit(oldLimit);
            return (T)result;
        }

        @Override
        public ByteString readBytes() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.limit - this.pos) {
                ByteString result = this.immutable && this.enableAliasing ? ByteString.wrap(this.buffer, this.pos, size) : ByteString.copyFrom(this.buffer, this.pos, size);
                this.pos += size;
                return result;
            }
            if (size == 0) {
                return ByteString.EMPTY;
            }
            return ByteString.wrap(this.readRawBytes(size));
        }

        @Override
        public byte[] readByteArray() throws IOException {
            int size = this.readRawVarint32();
            return this.readRawBytes(size);
        }

        @Override
        public ByteBuffer readByteBuffer() throws IOException {
            int size = this.readRawVarint32();
            if (size > 0 && size <= this.limit - this.pos) {
                ByteBuffer result = !this.immutable && this.enableAliasing ? ByteBuffer.wrap(this.buffer, this.pos, size).slice() : ByteBuffer.wrap(Arrays.copyOfRange(this.buffer, this.pos, this.pos + size));
                this.pos += size;
                return result;
            }
            if (size == 0) {
                return Internal.EMPTY_BYTE_BUFFER;
            }
            if (size < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public int readUInt32() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public int readEnum() throws IOException {
            return this.readRawVarint32();
        }

        @Override
        public int readSFixed32() throws IOException {
            return this.readRawLittleEndian32();
        }

        @Override
        public long readSFixed64() throws IOException {
            return this.readRawLittleEndian64();
        }

        @Override
        public int readSInt32() throws IOException {
            return ArrayDecoder.decodeZigZag32(this.readRawVarint32());
        }

        @Override
        public long readSInt64() throws IOException {
            return ArrayDecoder.decodeZigZag64(this.readRawVarint64());
        }

        @Override
        public int readRawVarint32() throws IOException {
            block4: {
                int x;
                int tempPos;
                block6: {
                    byte[] buffer;
                    block8: {
                        block7: {
                            block5: {
                                tempPos = this.pos;
                                if (this.limit == tempPos) break block4;
                                buffer = this.buffer;
                                if ((x = buffer[tempPos++]) >= 0) {
                                    this.pos = tempPos;
                                    return x;
                                }
                                if (this.limit - tempPos < 9) break block4;
                                if ((x ^= buffer[tempPos++] << 7) >= 0) break block5;
                                x ^= 0xFFFFFF80;
                                break block6;
                            }
                            if ((x ^= buffer[tempPos++] << 14) < 0) break block7;
                            x ^= 0x3F80;
                            break block6;
                        }
                        if ((x ^= buffer[tempPos++] << 21) >= 0) break block8;
                        x ^= 0xFFE03F80;
                        break block6;
                    }
                    byte y = buffer[tempPos++];
                    x ^= y << 28;
                    x ^= 0xFE03F80;
                    if (y < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0 && buffer[tempPos++] < 0) break block4;
                }
                this.pos = tempPos;
                return x;
            }
            return (int)this.readRawVarint64SlowPath();
        }

        private void skipRawVarint() throws IOException {
            if (this.limit - this.pos >= 10) {
                this.skipRawVarintFastPath();
            } else {
                this.skipRawVarintSlowPath();
            }
        }

        private void skipRawVarintFastPath() throws IOException {
            for (int i = 0; i < 10; ++i) {
                if (this.buffer[this.pos++] < 0) continue;
                return;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        private void skipRawVarintSlowPath() throws IOException {
            for (int i = 0; i < 10; ++i) {
                if (this.readRawByte() < 0) continue;
                return;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        @Override
        public long readRawVarint64() throws IOException {
            block4: {
                long x;
                int tempPos;
                block6: {
                    byte[] buffer;
                    block12: {
                        block11: {
                            block10: {
                                block9: {
                                    int y;
                                    block8: {
                                        block7: {
                                            block5: {
                                                tempPos = this.pos;
                                                if (this.limit == tempPos) break block4;
                                                buffer = this.buffer;
                                                if ((y = buffer[tempPos++]) >= 0) {
                                                    this.pos = tempPos;
                                                    return y;
                                                }
                                                if (this.limit - tempPos < 9) break block4;
                                                if ((y ^= buffer[tempPos++] << 7) >= 0) break block5;
                                                x = y ^ 0xFFFFFF80;
                                                break block6;
                                            }
                                            if ((y ^= buffer[tempPos++] << 14) < 0) break block7;
                                            x = y ^ 0x3F80;
                                            break block6;
                                        }
                                        if ((y ^= buffer[tempPos++] << 21) >= 0) break block8;
                                        x = y ^ 0xFFE03F80;
                                        break block6;
                                    }
                                    if ((x = (long)y ^ (long)buffer[tempPos++] << 28) < 0L) break block9;
                                    x ^= 0xFE03F80L;
                                    break block6;
                                }
                                if ((x ^= (long)buffer[tempPos++] << 35) >= 0L) break block10;
                                x ^= 0xFFFFFFF80FE03F80L;
                                break block6;
                            }
                            if ((x ^= (long)buffer[tempPos++] << 42) < 0L) break block11;
                            x ^= 0x3F80FE03F80L;
                            break block6;
                        }
                        if ((x ^= (long)buffer[tempPos++] << 49) >= 0L) break block12;
                        x ^= 0xFFFE03F80FE03F80L;
                        break block6;
                    }
                    x ^= (long)buffer[tempPos++] << 56;
                    if ((x ^= 0xFE03F80FE03F80L) < 0L && (long)buffer[tempPos++] < 0L) break block4;
                }
                this.pos = tempPos;
                return x;
            }
            return this.readRawVarint64SlowPath();
        }

        @Override
        long readRawVarint64SlowPath() throws IOException {
            long result = 0L;
            for (int shift = 0; shift < 64; shift += 7) {
                byte b = this.readRawByte();
                result |= (long)(b & 0x7F) << shift;
                if ((b & 0x80) != 0) continue;
                return result;
            }
            throw InvalidProtocolBufferException.malformedVarint();
        }

        @Override
        public int readRawLittleEndian32() throws IOException {
            int tempPos = this.pos;
            if (this.limit - tempPos < 4) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            byte[] buffer = this.buffer;
            this.pos = tempPos + 4;
            return buffer[tempPos] & 0xFF | (buffer[tempPos + 1] & 0xFF) << 8 | (buffer[tempPos + 2] & 0xFF) << 16 | (buffer[tempPos + 3] & 0xFF) << 24;
        }

        @Override
        public long readRawLittleEndian64() throws IOException {
            int tempPos = this.pos;
            if (this.limit - tempPos < 8) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            byte[] buffer = this.buffer;
            this.pos = tempPos + 8;
            return (long)buffer[tempPos] & 0xFFL | ((long)buffer[tempPos + 1] & 0xFFL) << 8 | ((long)buffer[tempPos + 2] & 0xFFL) << 16 | ((long)buffer[tempPos + 3] & 0xFFL) << 24 | ((long)buffer[tempPos + 4] & 0xFFL) << 32 | ((long)buffer[tempPos + 5] & 0xFFL) << 40 | ((long)buffer[tempPos + 6] & 0xFFL) << 48 | ((long)buffer[tempPos + 7] & 0xFFL) << 56;
        }

        @Override
        public void enableAliasing(boolean enabled) {
            this.enableAliasing = enabled;
        }

        @Override
        public void resetSizeCounter() {
            this.startPos = this.pos;
        }

        @Override
        public int pushLimit(int byteLimit) throws InvalidProtocolBufferException {
            int oldLimit;
            if (byteLimit < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            if ((byteLimit += this.getTotalBytesRead()) > (oldLimit = this.currentLimit)) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            this.currentLimit = byteLimit;
            this.recomputeBufferSizeAfterLimit();
            return oldLimit;
        }

        private void recomputeBufferSizeAfterLimit() {
            this.limit += this.bufferSizeAfterLimit;
            int bufferEnd = this.limit - this.startPos;
            if (bufferEnd > this.currentLimit) {
                this.bufferSizeAfterLimit = bufferEnd - this.currentLimit;
                this.limit -= this.bufferSizeAfterLimit;
            } else {
                this.bufferSizeAfterLimit = 0;
            }
        }

        @Override
        public void popLimit(int oldLimit) {
            this.currentLimit = oldLimit;
            this.recomputeBufferSizeAfterLimit();
        }

        @Override
        public int getBytesUntilLimit() {
            if (this.currentLimit == Integer.MAX_VALUE) {
                return -1;
            }
            return this.currentLimit - this.getTotalBytesRead();
        }

        @Override
        public boolean isAtEnd() throws IOException {
            return this.pos == this.limit;
        }

        @Override
        public int getTotalBytesRead() {
            return this.pos - this.startPos;
        }

        @Override
        public byte readRawByte() throws IOException {
            if (this.pos == this.limit) {
                throw InvalidProtocolBufferException.truncatedMessage();
            }
            return this.buffer[this.pos++];
        }

        @Override
        public byte[] readRawBytes(int length) throws IOException {
            if (length > 0 && length <= this.limit - this.pos) {
                int tempPos = this.pos;
                this.pos += length;
                return Arrays.copyOfRange(this.buffer, tempPos, this.pos);
            }
            if (length <= 0) {
                if (length == 0) {
                    return Internal.EMPTY_BYTE_ARRAY;
                }
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }

        @Override
        public void skipRawBytes(int length) throws IOException {
            if (length >= 0 && length <= this.limit - this.pos) {
                this.pos += length;
                return;
            }
            if (length < 0) {
                throw InvalidProtocolBufferException.negativeSize();
            }
            throw InvalidProtocolBufferException.truncatedMessage();
        }
    }
}

