package cn.lliiooll.bc.command;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.data.PlayerChatData;
import cn.lliiooll.bc.utils.ConfigManager;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import net.md_5.bungee.api.CommandSender;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.plugin.Command;
import net.md_5.bungee.api.plugin.TabExecutor;

public class ChatViewCommand extends Command implements TabExecutor {
    
    public ChatViewCommand() {
        super("chatview", null, "cv");
    }

    @Override
    public void execute(CommandSender sender, String[] args) {
        if (!(sender instanceof ProxiedPlayer)) {
            sender.sendMessage("§c只有玩家可以使用此指令");
            return;
        }
        
        ProxiedPlayer player = (ProxiedPlayer) sender;
        
        if (args.length == 0) {
            sendUsage(player);
            return;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "yaml":
            case "y":
                if (args.length < 2) {
                    player.sendMessage("§c用法: /chatview yaml <玩家名>");
                    return;
                }
                viewYamlFile(player, args[1]);
                break;
            case "raw":
            case "r":
                if (args.length < 2) {
                    player.sendMessage("§c用法: /chatview raw <玩家名>");
                    return;
                }
                viewRawData(player, args[1]);
                break;
            case "list":
            case "l":
                listYamlFiles(player);
                break;
            default:
                sendUsage(player);
                break;
        }
    }
    
    private void sendUsage(ProxiedPlayer player) {
        player.sendMessage("§6=== PKChatRecord 查看命令 ===");
        player.sendMessage("§e/chatview yaml <玩家名> §7- 查看玩家的YAML文件内容（可读格式）");
        player.sendMessage("§e/chatview raw <玩家名> §7- 查看原始数据");
        player.sendMessage("§e/chatview list §7- 列出所有YAML文件");
        player.sendMessage("§7别名: §b/cv");
    }
    
    private void viewYamlFile(ProxiedPlayer player, String playerName) {
        File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        File yamlFile = new File(dataDir, playerName.toLowerCase() + ".yml");
        
        if (!yamlFile.exists()) {
            player.sendMessage("§c玩家 " + playerName + " 的YAML文件不存在");
            return;
        }
        
        try {
            PlayerChatData data = ConfigManager.getRecord(playerName);
            if (data == null) {
                player.sendMessage("§c无法读取玩家数据");
                return;
            }
            
            player.sendMessage("§6=== " + data.getName() + " 的聊天记录（YAML格式）===");
            player.sendMessage("§7玩家名: §f" + data.getName());
            player.sendMessage("§7UUID: §f" + data.getUuid());
            player.sendMessage("§7总消息数: §f" + data.getChatsCount());
            player.sendMessage("§7文件大小: §f" + yamlFile.length() + " 字节");
            player.sendMessage("§6=== 最近10条消息（可读格式）===");
            
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<PlayerChatData.ChatData> chats = data.getChatsList();
            
            // 显示最后10条消息
            int start = Math.max(0, chats.size() - 10);
            for (int i = start; i < chats.size(); i++) {
                PlayerChatData.ChatData chat = chats.get(i);
                Date date = new Date(chat.getTime());
                String timeStr = format.format(date);
                
                player.sendMessage("§7[" + timeStr + "] §a" + chat.getServer() + " §f" + chat.getContent());
                player.sendMessage("§8  └─ 时间戳: " + chat.getTime());
            }
            
        } catch (Exception e) {
            player.sendMessage("§c读取文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void viewRawData(ProxiedPlayer player, String playerName) {
        try {
            // 直接读取YAML文件内容
            File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
            File yamlFile = new File(dataDir, playerName.toLowerCase() + ".yml");
            
            if (!yamlFile.exists()) {
                player.sendMessage("§c玩家 " + playerName + " 的YAML文件不存在");
                return;
            }
            
            java.nio.file.Path path = yamlFile.toPath();
            List<String> lines = java.nio.file.Files.readAllLines(path);
            
            player.sendMessage("§6=== " + playerName + " 的原始YAML文件内容 ===");
            player.sendMessage("§7文件路径: §f" + yamlFile.getAbsolutePath());
            player.sendMessage("§7文件大小: §f" + yamlFile.length() + " 字节");
            player.sendMessage("§6=== YAML内容 ===");
            
            int lineCount = 0;
            for (String line : lines) {
                lineCount++;
                if (lineCount > 50) { // 限制显示行数
                    player.sendMessage("§7... (文件太长，只显示前50行)");
                    break;
                }
                player.sendMessage("§7" + String.format("%3d", lineCount) + ": §f" + line);
            }
            
        } catch (Exception e) {
            player.sendMessage("§c读取文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void listYamlFiles(ProxiedPlayer player) {
        File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        
        if (!dataDir.exists()) {
            player.sendMessage("§c数据目录不存在");
            return;
        }
        
        File[] yamlFiles = dataDir.listFiles((dir, name) -> name.endsWith(".yml"));
        
        if (yamlFiles == null || yamlFiles.length == 0) {
            player.sendMessage("§c没有找到任何YAML文件");
            return;
        }
        
        player.sendMessage("§6=== YAML聊天文件列表 ===");
        player.sendMessage("§7总共 " + yamlFiles.length + " 个文件");
        
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (File file : yamlFiles) {
            String playerName = file.getName().replace(".yml", "");
            long size = file.length();
            Date lastModified = new Date(file.lastModified());
            
            player.sendMessage("§a" + playerName + " §7- §f" + size + " 字节 §7- §f" + format.format(lastModified));
        }
    }

    @Override
    public Iterable<String> onTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // 第一个参数：子命令
            String partial = args[0].toLowerCase();
            if ("yaml".startsWith(partial)) completions.add("yaml");
            if ("raw".startsWith(partial)) completions.add("raw");
            if ("list".startsWith(partial)) completions.add("list");
        } else if (args.length == 2 && (args[0].equalsIgnoreCase("yaml") || args[0].equalsIgnoreCase("raw"))) {
            // 玩家名补全
            String partial = args[1].toLowerCase();
            
            // 从在线玩家补全
            for (ProxiedPlayer player : PKChatRecord.INSTANCE.getProxy().getPlayers()) {
                if (player.getName().toLowerCase().startsWith(partial)) {
                    completions.add(player.getName());
                }
            }
            
            // 从YAML文件补全
            File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
            if (dataDir.exists()) {
                File[] yamlFiles = dataDir.listFiles((dir, name) -> name.endsWith(".yml"));
                if (yamlFiles != null) {
                    for (File file : yamlFiles) {
                        String playerName = file.getName().replace(".yml", "");
                        if (playerName.toLowerCase().startsWith(partial) && !completions.contains(playerName)) {
                            completions.add(playerName);
                        }
                    }
                }
            }
        }
        
        return completions;
    }
}
