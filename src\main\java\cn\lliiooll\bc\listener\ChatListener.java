/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  net.md_5.bungee.api.connection.ProxiedPlayer
 *  net.md_5.bungee.api.event.ChatEvent
 *  net.md_5.bungee.api.plugin.Listener
 *  net.md_5.bungee.event.EventHandler
 */
package cn.lliiooll.bc.listener;

import cn.lliiooll.bc.utils.ConfigManager;
import java.util.List;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.event.ChatEvent;
import net.md_5.bungee.api.plugin.Listener;
import net.md_5.bungee.event.EventHandler;

public class ChatListener
        implements Listener {

    @EventHandler
    public void onChat(ChatEvent event) {
        if (event.getSender() instanceof ProxiedPlayer) {
            ProxiedPlayer player = (ProxiedPlayer) event.getSender();
            String message = event.getMessage();

            // 快速检查是否应该忽略此消息
            if (shouldIgnoreMessage(message)) {
                return;
            }

            // 异步记录聊天消息，避免阻塞主线程
            ConfigManager.recordAsync(message, player);
        }
    }

    /**
     * 检查消息是否应该被忽略
     * 使用缓存的忽略列表提高性能
     */
    private boolean shouldIgnoreMessage(String message) {
        List<String> ignores = ConfigManager.getCachedIgnoreList();
        for (String ignore : ignores) {
            if (message.startsWith(ignore)) {
                return true;
            }
        }
        return false;
    }
}
