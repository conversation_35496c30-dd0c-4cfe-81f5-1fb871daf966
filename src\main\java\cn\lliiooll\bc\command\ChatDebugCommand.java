package cn.lliiooll.bc.command;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.data.PlayerChatDataOuterClass;
import cn.lliiooll.bc.utils.FileUtils;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import net.md_5.bungee.api.CommandSender;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.plugin.Command;
import net.md_5.bungee.api.plugin.TabExecutor;

public class ChatDebugCommand extends Command implements TabExecutor {
    
    public ChatDebugCommand() {
        super("chatdebug", null, "cd");
    }

    @Override
    public void execute(CommandSender sender, String[] args) {
        if (!(sender instanceof ProxiedPlayer)) {
            sender.sendMessage("§c只有玩家可以使用此指令");
            return;
        }
        
        ProxiedPlayer player = (ProxiedPlayer) sender;
        
        if (args.length == 0) {
            sendUsage(player);
            return;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "view":
            case "v":
                if (args.length < 2) {
                    player.sendMessage("§c用法: /chatdebug view <玩家名>");
                    return;
                }
                viewChatFile(player, args[1]);
                break;
            case "list":
            case "l":
                listChatFiles(player);
                break;
            case "info":
            case "i":
                if (args.length < 2) {
                    player.sendMessage("§c用法: /chatdebug info <玩家名>");
                    return;
                }
                showFileInfo(player, args[1]);
                break;
            default:
                sendUsage(player);
                break;
        }
    }
    
    private void sendUsage(ProxiedPlayer player) {
        player.sendMessage("§6=== PKChatRecord 调试命令 ===");
        player.sendMessage("§e/chatdebug view <玩家名> §7- 查看玩家的聊天文件内容");
        player.sendMessage("§e/chatdebug list §7- 列出所有聊天文件");
        player.sendMessage("§e/chatdebug info <玩家名> §7- 显示文件信息");
        player.sendMessage("§7别名: §b/cd");
    }
    
    private void viewChatFile(ProxiedPlayer player, String playerName) {
        File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        File chatFile = new File(dataDir, playerName.toLowerCase() + ".chat");
        
        if (!chatFile.exists()) {
            player.sendMessage("§c玩家 " + playerName + " 的聊天文件不存在");
            return;
        }
        
        try {
            PlayerChatDataOuterClass.PlayerChatData data = PlayerChatDataOuterClass.PlayerChatData
                    .parseFrom(FileUtils.read(chatFile));
            
            player.sendMessage("§6=== " + data.getName() + " 的聊天记录 ===");
            player.sendMessage("§7UUID: §f" + data.getUuid());
            player.sendMessage("§7总消息数: §f" + data.getChatsCount());
            player.sendMessage("§7文件大小: §f" + chatFile.length() + " 字节");
            player.sendMessage("§6=== 最近10条消息 ===");
            
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<PlayerChatDataOuterClass.PlayerChatData.ChatData> chats = data.getChatsList();
            
            // 显示最后10条消息
            int start = Math.max(0, chats.size() - 10);
            for (int i = start; i < chats.size(); i++) {
                PlayerChatDataOuterClass.PlayerChatData.ChatData chat = chats.get(i);
                Date date = new Date(chat.getTime());
                String timeStr = format.format(date);
                
                player.sendMessage("§7[" + timeStr + "] §a" + chat.getServer() + " §f" + chat.getContent());
            }
            
        } catch (Exception e) {
            player.sendMessage("§c读取文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void listChatFiles(ProxiedPlayer player) {
        File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        
        if (!dataDir.exists()) {
            player.sendMessage("§c数据目录不存在");
            return;
        }
        
        File[] chatFiles = dataDir.listFiles((dir, name) -> name.endsWith(".chat"));
        
        if (chatFiles == null || chatFiles.length == 0) {
            player.sendMessage("§c没有找到任何聊天文件");
            return;
        }
        
        player.sendMessage("§6=== 聊天文件列表 ===");
        player.sendMessage("§7总共 " + chatFiles.length + " 个文件");
        
        for (File file : chatFiles) {
            String playerName = file.getName().replace(".chat", "");
            long size = file.length();
            Date lastModified = new Date(file.lastModified());
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            
            player.sendMessage("§a" + playerName + " §7- §f" + size + " 字节 §7- §f" + format.format(lastModified));
        }
    }
    
    private void showFileInfo(ProxiedPlayer player, String playerName) {
        File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        File chatFile = new File(dataDir, playerName.toLowerCase() + ".chat");
        
        if (!chatFile.exists()) {
            player.sendMessage("§c玩家 " + playerName + " 的聊天文件不存在");
            return;
        }
        
        try {
            PlayerChatDataOuterClass.PlayerChatData data = PlayerChatDataOuterClass.PlayerChatData
                    .parseFrom(FileUtils.read(chatFile));
            
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date fileDate = new Date(chatFile.lastModified());
            
            player.sendMessage("§6=== " + data.getName() + " 文件信息 ===");
            player.sendMessage("§7玩家名: §f" + data.getName());
            player.sendMessage("§7UUID: §f" + data.getUuid());
            player.sendMessage("§7消息总数: §f" + data.getChatsCount());
            player.sendMessage("§7文件大小: §f" + chatFile.length() + " 字节");
            player.sendMessage("§7最后修改: §f" + format.format(fileDate));
            player.sendMessage("§7文件路径: §f" + chatFile.getAbsolutePath());
            
            if (data.getChatsCount() > 0) {
                // 第一条和最后一条消息的时间
                PlayerChatDataOuterClass.PlayerChatData.ChatData firstChat = data.getChats(0);
                PlayerChatDataOuterClass.PlayerChatData.ChatData lastChat = data.getChats(data.getChatsCount() - 1);
                
                Date firstTime = new Date(firstChat.getTime());
                Date lastTime = new Date(lastChat.getTime());
                
                player.sendMessage("§7第一条消息: §f" + format.format(firstTime));
                player.sendMessage("§7最后一条消息: §f" + format.format(lastTime));
            }
            
        } catch (Exception e) {
            player.sendMessage("§c读取文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Iterable<String> onTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // 第一个参数：子命令
            String partial = args[0].toLowerCase();
            if ("view".startsWith(partial)) completions.add("view");
            if ("list".startsWith(partial)) completions.add("list");
            if ("info".startsWith(partial)) completions.add("info");
        } else if (args.length == 2 && (args[0].equalsIgnoreCase("view") || args[0].equalsIgnoreCase("info"))) {
            // 玩家名补全
            String partial = args[1].toLowerCase();
            
            // 从在线玩家补全
            for (ProxiedPlayer player : PKChatRecord.INSTANCE.getProxy().getPlayers()) {
                if (player.getName().toLowerCase().startsWith(partial)) {
                    completions.add(player.getName());
                }
            }
            
            // 从聊天文件补全
            File dataDir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
            if (dataDir.exists()) {
                File[] chatFiles = dataDir.listFiles((dir, name) -> name.endsWith(".chat"));
                if (chatFiles != null) {
                    for (File file : chatFiles) {
                        String playerName = file.getName().replace(".chat", "");
                        if (playerName.toLowerCase().startsWith(partial) && !completions.contains(playerName)) {
                            completions.add(playerName);
                        }
                    }
                }
            }
        }
        
        return completions;
    }
}
