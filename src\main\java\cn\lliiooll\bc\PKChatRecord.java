/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  net.luckperms.api.LuckPerms
 *  net.luckperms.api.LuckPermsProvider
 *  net.md_5.bungee.api.plugin.Command
 *  net.md_5.bungee.api.plugin.Listener
 *  net.md_5.bungee.api.plugin.Plugin
 */
package cn.lliiooll.bc;

import cn.lliiooll.bc.command.ChatRecordCommand;
import cn.lliiooll.bc.command.ChatSearchCommand;
import cn.lliiooll.bc.command.ChatViewCommand;
import cn.lliiooll.bc.listener.ChatListener;
import cn.lliiooll.bc.utils.ConfigManager;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.LuckPermsProvider;
import net.md_5.bungee.api.plugin.Command;
import net.md_5.bungee.api.plugin.Listener;
import net.md_5.bungee.api.plugin.Plugin;

public final class PKChatRecord
        extends Plugin {
    public static PKChatRecord INSTANCE;
    public static LuckPerms luckPerms;

    public void onLoad() {
        INSTANCE = this;
    }

    public void onEnable() {
        this.getLogger().info("§a正在启动 PKChatRecord 插件...");

        try {
            // 注册监听器
            this.getProxy().getPluginManager().registerListener((Plugin) this, (Listener) new ChatListener());
            this.getLogger().info("§a聊天监听器注册成功");

            // 注册命令
            this.getProxy().getPluginManager().registerCommand((Plugin) this, (Command) new ChatRecordCommand());
            this.getLogger().info("§a命令 /chatrecord 注册成功");

            this.getProxy().getPluginManager().registerCommand((Plugin) this, (Command) new ChatSearchCommand());
            this.getLogger().info("§a命令 /chatsearch 注册成功");

            this.getProxy().getPluginManager().registerCommand((Plugin) this, (Command) new ChatViewCommand());
            this.getLogger().info("§a命令 /chatview 注册成功");

            // 获取 LuckPerms
            luckPerms = LuckPermsProvider.get();
            this.getLogger().info("§aLuckPerms 集成成功");

            // 初始化配置
            ConfigManager.init();
            this.getLogger().info("§a配置文件初始化成功");

            this.getLogger().info("§2PKChatRecord 插件启动完毕! 版本: " + this.getDescription().getVersion());
        } catch (Exception e) {
            this.getLogger().severe("§c插件启动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void onDisable() {
        // 清理异步执行器资源
        ConfigManager.shutdown();
        this.getLogger().info("\u00a72\u804a\u5929\u67e5\u8be2\u63d2\u4ef6\u5173\u95ed\u5b8c\u6bd5!");
    }
}
