/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  net.luckperms.api.LuckPerms
 *  net.luckperms.api.LuckPermsProvider
 *  net.md_5.bungee.api.plugin.Command
 *  net.md_5.bungee.api.plugin.Listener
 *  net.md_5.bungee.api.plugin.Plugin
 */
package cn.lliiooll.bc;

import cn.lliiooll.bc.command.ChatRecordCommand;
import cn.lliiooll.bc.listener.ChatListener;
import cn.lliiooll.bc.utils.ConfigManager;
import net.luckperms.api.LuckPerms;
import net.luckperms.api.LuckPermsProvider;
import net.md_5.bungee.api.plugin.Command;
import net.md_5.bungee.api.plugin.Listener;
import net.md_5.bungee.api.plugin.Plugin;

public final class PKChatRecord
extends Plugin {
    public static PKChatRecord INSTANCE;
    public static LuckPerms luckPerms;

    public void onLoad() {
        INSTANCE = this;
    }

    public void onEnable() {
        this.getProxy().getPluginManager().registerListener((Plugin)this, (Listener)new ChatListener());
        this.getProxy().getPluginManager().registerCommand((Plugin)this, (Command)new ChatRecordCommand());
        luckPerms = LuckPermsProvider.get();
        ConfigManager.init();
        this.getLogger().info("\u00a72\u804a\u5929\u67e5\u8be2\u63d2\u4ef6\u542f\u52a8\u5b8c\u6bd5!");
    }

    public void onDisable() {
        this.getLogger().info("\u00a72\u804a\u5929\u67e5\u8be2\u63d2\u4ef6\u542f\u52a8\u5b8c\u6bd5!");
    }
}

