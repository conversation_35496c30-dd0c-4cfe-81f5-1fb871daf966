package cn.lliiooll.bc.data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import net.md_5.bungee.config.Configuration;

/**
 * 玩家聊天数据类，替代 protobuf
 */
public class PlayerChatData {
    private String name;
    private String uuid;
    private List<ChatData> chats;

    public PlayerChatData() {
        this.chats = new ArrayList<>();
    }

    public PlayerChatData(String name, String uuid) {
        this.name = name;
        this.uuid = uuid;
        this.chats = new ArrayList<>();
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public List<ChatData> getChats() {
        return chats;
    }

    public void setChats(List<ChatData> chats) {
        this.chats = chats;
    }

    public int getChatsCount() {
        return chats.size();
    }

    public ChatData getChat(int index) {
        return chats.get(index);
    }

    public List<ChatData> getChatsList() {
        return new ArrayList<>(chats);
    }

    /**
     * 从 Configuration 创建 PlayerChatData
     */
    public static PlayerChatData fromConfiguration(Configuration config) {
        PlayerChatData data = new PlayerChatData();
        data.setName(config.getString("name", ""));
        data.setUuid(config.getString("uuid", ""));

        List<?> chatsList = config.getList("chats", new ArrayList<>());
        List<ChatData> chats = new ArrayList<>();

        for (Object chatObj : chatsList) {
            if (chatObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> chatMap = (Map<String, Object>) chatObj;
                ChatData chatData = new ChatData();
                chatData.setContent((String) chatMap.get("content"));
                chatData.setServer((String) chatMap.get("server"));

                // 优先使用 timestamp 字段，如果没有则尝试解析 time 字段
                Object timestampObj = chatMap.get("timestamp");
                if (timestampObj instanceof Number) {
                    chatData.setTime(((Number) timestampObj).longValue());
                } else {
                    // 兼容旧格式或解析时间字符串
                    Object timeObj = chatMap.get("time");
                    if (timeObj instanceof Number) {
                        chatData.setTime(((Number) timeObj).longValue());
                    } else if (timeObj instanceof String) {
                        String timeStr = (String) timeObj;
                        try {
                            // 尝试解析为时间戳
                            chatData.setTime(Long.parseLong(timeStr));
                        } catch (NumberFormatException e) {
                            try {
                                // 尝试解析为时间格式
                                java.text.SimpleDateFormat format = new java.text.SimpleDateFormat(
                                        "yyyy-MM-dd HH:mm:ss");
                                java.util.Date date = format.parse(timeStr);
                                chatData.setTime(date.getTime());
                            } catch (java.text.ParseException pe) {
                                // 解析失败，使用当前时间
                                chatData.setTime(System.currentTimeMillis());
                            }
                        }
                    } else {
                        chatData.setTime(System.currentTimeMillis());
                    }
                }

                // 设置可读时间字符串
                Object readableTimeObj = chatMap.get("time");
                if (readableTimeObj instanceof String && !((String) readableTimeObj).matches("\\d+")) {
                    chatData.setReadableTime((String) readableTimeObj);
                } else {
                    // 如果没有可读时间，从时间戳生成
                    java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    chatData.setReadableTime(format.format(new java.util.Date(chatData.getTime())));
                }

                chats.add(chatData);
            }
        }

        data.setChats(chats);
        return data;
    }

    /**
     * 聊天数据类
     */
    public static class ChatData {
        private String content;
        private String server;
        private long time;
        private String readableTime;

        public ChatData() {
        }

        public ChatData(String content, String server, long time) {
            this.content = content;
            this.server = server;
            this.time = time;
        }

        // Getters and Setters
        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getServer() {
            return server;
        }

        public void setServer(String server) {
            this.server = server;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public String getReadableTime() {
            return readableTime;
        }

        public void setReadableTime(String readableTime) {
            this.readableTime = readableTime;
        }
    }
}
