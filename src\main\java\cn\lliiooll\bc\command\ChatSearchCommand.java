package cn.lliiooll.bc.command;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.utils.ConfigManager;
import cn.lliiooll.bc.utils.StringUtils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.luckperms.api.model.user.User;
import net.md_5.bungee.api.CommandSender;
import net.md_5.bungee.api.chat.ClickEvent;
import net.md_5.bungee.api.chat.ComponentBuilder;
import net.md_5.bungee.api.chat.HoverEvent;
import net.md_5.bungee.api.chat.TextComponent;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.plugin.Command;
import net.md_5.bungee.api.plugin.TabExecutor;

public class ChatSearchCommand extends Command implements TabExecutor {

    public ChatSearchCommand() {
        super("chatsearch", null, "cs");
    }

    @Override
    public void execute(CommandSender sender, String[] args) {
        if (!(sender instanceof ProxiedPlayer)) {
            sender.sendMessage("§c只有玩家可以使用此指令");
            return;
        }

        ProxiedPlayer player = (ProxiedPlayer) sender;

        if (args.length == 0) {
            sendUsage(player);
            return;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "search":
            case "s":
                handleSearch(player, args);
                break;
            case "list":
            case "l":
                handleList(player, args);
                break;
            case "player":
            case "p":
                handlePlayerSearch(player, args);
                break;
            default:
                sendUsage(player);
                break;
        }
    }

    private void sendUsage(ProxiedPlayer player) {
        player.sendMessage("§6=== PKChatRecord 搜索命令 ===");
        player.sendMessage("§e/chatsearch search <关键词> [页数] §7- 搜索所有玩家的聊天记录");
        player.sendMessage("§e/chatsearch player <玩家名> <关键词> [页数] §7- 搜索指定玩家的聊天记录");
        player.sendMessage("§e/chatsearch list [页数] §7- 列出所有有记录的玩家");
        player.sendMessage("§7别名: §b/cs");
    }

    private void handleSearch(ProxiedPlayer player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§c用法: /chatsearch search <关键词> [页数]");
            return;
        }

        String keyword = args[1];
        int page = 1;

        if (args.length >= 3 && StringUtils.isNumber(args[2])) {
            page = Integer.parseInt(args[2]);
        }

        searchAndDisplay(player, keyword, null, page);
    }

    private void handlePlayerSearch(ProxiedPlayer player, String[] args) {
        if (args.length < 3) {
            player.sendMessage("§c用法: /chatsearch player <玩家名> <关键词> [页数]");
            return;
        }

        String playerName = args[1];
        String keyword = args[2];
        int page = 1;

        if (args.length >= 4 && StringUtils.isNumber(args[3])) {
            page = Integer.parseInt(args[3]);
        }

        searchAndDisplay(player, keyword, playerName, page);
    }

    private void handleList(ProxiedPlayer player, String[] args) {
        int page = 1;
        if (args.length >= 2 && StringUtils.isNumber(args[1])) {
            page = Integer.parseInt(args[1]);
        }

        List<String> players = ConfigManager.getAllPlayersWithRecords();
        displayPlayerList(player, players, page);
    }

    private void searchAndDisplay(ProxiedPlayer player, String keyword, String targetPlayer, int page) {
        // 异步执行搜索，避免阻塞主线程
        PKChatRecord.INSTANCE.getProxy().getScheduler().runAsync(PKChatRecord.INSTANCE, () -> {
            List<ConfigManager.SearchResult> results = ConfigManager.searchChatRecords(keyword, targetPlayer, 100);

            // 回到主线程显示结果
            PKChatRecord.INSTANCE.getProxy().getScheduler().schedule(PKChatRecord.INSTANCE, () -> {
                displaySearchResults(player, results, keyword, targetPlayer, page);
            }, 0, java.util.concurrent.TimeUnit.MILLISECONDS);
        });
    }

    private void displaySearchResults(ProxiedPlayer player, List<ConfigManager.SearchResult> results,
            String keyword, String targetPlayer, int page) {
        if (results.isEmpty()) {
            if (targetPlayer != null) {
                player.sendMessage("§c在玩家 " + targetPlayer + " 的聊天记录中未找到包含 \"" + keyword + "\" 的消息");
            } else {
                player.sendMessage("§c未找到包含 \"" + keyword + "\" 的聊天记录");
            }
            return;
        }

        int pageSize = ConfigManager.getDefault().getInt("line", 5);
        int totalPages = (int) Math.ceil((double) results.size() / pageSize);

        if (page < 1 || page > totalPages) {
            player.sendMessage("§c页数无效，总共 " + totalPages + " 页");
            return;
        }

        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, results.size());

        // 显示搜索结果
        String searchInfo = targetPlayer != null ? "搜索玩家: " + targetPlayer + ", 关键词: " + keyword : "搜索关键词: " + keyword;

        player.sendMessage("§7§m=======§b[" + page + "/" + totalPages + "]§7§m=======");
        player.sendMessage("§6" + searchInfo + " §7(共找到 " + results.size() + " 条记录)");

        SimpleDateFormat format = new SimpleDateFormat(ConfigManager.getDefault().getString("time"));

        for (int i = startIndex; i < endIndex; i++) {
            ConfigManager.SearchResult result = results.get(i);
            Date date = new Date(result.getTime());
            String time = format.format(date);

            // 获取玩家前缀
            User user = PKChatRecord.luckPerms.getUserManager().getUser(result.getPlayerName());
            final String prefix;
            if (user != null && user.getCachedData().getMetaData().getPrefix() != null) {
                prefix = user.getCachedData().getMetaData().getPrefix();
            } else {
                prefix = "";
            }

            final String serverName = ConfigManager.getDefault().getString("servers." + result.getServer(),
                    result.getServer());
            final String finalTime = time;

            String message = StringUtils.replaceAll(ConfigManager.getDefault().getString("format"),
                    new HashMap<String, Object>() {
                        {
                            put("<time>", finalTime);
                            put("<server>", serverName);
                            put("<msg>", result.getMessage());
                            put("<n>", result.getPlayerName());
                            put("<prefix>", prefix);
                        }
                    });

            player.sendMessage(message);
        }

        // 发送可点击的翻页栏
        sendSearchPagination(player, keyword, targetPlayer, page, totalPages);
    }

    private void displayPlayerList(ProxiedPlayer player, List<String> players, int page) {
        if (players.isEmpty()) {
            player.sendMessage("§c没有找到任何聊天记录");
            return;
        }

        int pageSize = 10; // 玩家列表每页显示10个
        int totalPages = (int) Math.ceil((double) players.size() / pageSize);

        if (page < 1 || page > totalPages) {
            player.sendMessage("§c页数无效，总共 " + totalPages + " 页");
            return;
        }

        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, players.size());

        player.sendMessage("§7§m=======§b[" + page + "/" + totalPages + "]§7§m=======");
        player.sendMessage("§6有聊天记录的玩家列表 §7(共 " + players.size() + " 个玩家)");

        for (int i = startIndex; i < endIndex; i++) {
            String playerName = players.get(i);
            TextComponent playerComponent = new TextComponent("§a" + playerName);
            playerComponent
                    .setClickEvent(new ClickEvent(ClickEvent.Action.SUGGEST_COMMAND, "/chatrecord " + playerName));
            playerComponent.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
                    new ComponentBuilder("§7点击查看 " + playerName + " 的聊天记录").create()));

            player.sendMessage(playerComponent);
        }

        // 发送翻页栏
        sendListPagination(player, page, totalPages);
    }

    private void sendSearchPagination(ProxiedPlayer player, String keyword, String targetPlayer,
            int currentPage, int totalPages) {
        TextComponent pagination = new TextComponent();

        // 构建命令
        String baseCommand = targetPlayer != null ? "/chatsearch player " + targetPlayer + " " + keyword + " "
                : "/chatsearch search " + keyword + " ";

        // 左边的分隔线
        pagination.addExtra(new TextComponent("§7§m======="));

        // 上一页按钮
        if (currentPage > 1) {
            TextComponent prevButton = new TextComponent("§a[上一页]");
            prevButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND,
                    baseCommand + (currentPage - 1)));
            prevButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
                    new ComponentBuilder("§7点击查看第 " + (currentPage - 1) + " 页").create()));
            pagination.addExtra(prevButton);
        } else {
            pagination.addExtra(new TextComponent("§8[上一页]"));
        }

        // 页数信息
        pagination.addExtra(new TextComponent("§b[" + currentPage + "/" + totalPages + "]"));

        // 下一页按钮
        if (currentPage < totalPages) {
            TextComponent nextButton = new TextComponent("§a[下一页]");
            nextButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND,
                    baseCommand + (currentPage + 1)));
            nextButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
                    new ComponentBuilder("§7点击查看第 " + (currentPage + 1) + " 页").create()));
            pagination.addExtra(nextButton);
        } else {
            pagination.addExtra(new TextComponent("§8[下一页]"));
        }

        // 右边的分隔线
        pagination.addExtra(new TextComponent("§7§m======="));

        player.sendMessage(pagination);
    }

    private void sendListPagination(ProxiedPlayer player, int currentPage, int totalPages) {
        TextComponent pagination = new TextComponent();

        // 左边的分隔线
        pagination.addExtra(new TextComponent("§7§m======="));

        // 上一页按钮
        if (currentPage > 1) {
            TextComponent prevButton = new TextComponent("§a[上一页]");
            prevButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND,
                    "/chatsearch list " + (currentPage - 1)));
            prevButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
                    new ComponentBuilder("§7点击查看第 " + (currentPage - 1) + " 页").create()));
            pagination.addExtra(prevButton);
        } else {
            pagination.addExtra(new TextComponent("§8[上一页]"));
        }

        // 页数信息
        pagination.addExtra(new TextComponent("§b[" + currentPage + "/" + totalPages + "]"));

        // 下一页按钮
        if (currentPage < totalPages) {
            TextComponent nextButton = new TextComponent("§a[下一页]");
            nextButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND,
                    "/chatsearch list " + (currentPage + 1)));
            nextButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT,
                    new ComponentBuilder("§7点击查看第 " + (currentPage + 1) + " 页").create()));
            pagination.addExtra(nextButton);
        } else {
            pagination.addExtra(new TextComponent("§8[下一页]"));
        }

        // 右边的分隔线
        pagination.addExtra(new TextComponent("§7§m======="));

        player.sendMessage(pagination);
    }

    @Override
    public Iterable<String> onTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：子命令
            String partial = args[0].toLowerCase();
            if ("search".startsWith(partial))
                completions.add("search");
            if ("player".startsWith(partial))
                completions.add("player");
            if ("list".startsWith(partial))
                completions.add("list");
        } else if (args.length == 2) {
            if (args[0].equalsIgnoreCase("player")) {
                // 玩家名补全
                String partial = args[1].toLowerCase();
                for (ProxiedPlayer player : PKChatRecord.INSTANCE.getProxy().getPlayers()) {
                    if (player.getName().toLowerCase().startsWith(partial)) {
                        completions.add(player.getName());
                    }
                }
                // 也可以从记录文件中获取玩家名
                for (String playerName : ConfigManager.getAllPlayersWithRecords()) {
                    if (playerName.toLowerCase().startsWith(partial) && !completions.contains(playerName)) {
                        completions.add(playerName);
                    }
                }
            }
        }

        return completions;
    }
}
