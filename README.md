# PKChatRecord

一个用于记录玩家聊天记录的BungeeCord插件。

## 功能特性

- 记录玩家在各个服务器的聊天消息
- 支持查询指定玩家的聊天历史
- 集成LuckPerms显示玩家前缀
- 支持分页查看聊天记录
- 可配置忽略特定命令
- 使用Protocol Buffers进行数据存储

## 依赖要求

- BungeeCord 1.19+
- LuckPerms插件
- Java 8+

## 构建项目

### 前提条件
- 安装Java 8或更高版本
- 安装Maven 3.6+

### 构建步骤

1. 克隆或下载项目源码
2. 在项目根目录执行以下命令：

```bash
mvn clean package
```

3. 构建完成后，在`target`目录下会生成`PKChatRecord-1.0.0.jar`文件

### 快速构建
```bash
mvn clean package -DskipTests
```

## 安装使用

1. 将构建生成的jar文件放入BungeeCord的`plugins`目录
2. 确保已安装LuckPerms插件
3. 重启BungeeCord服务器
4. 插件会自动生成配置文件`config.yml`

## 配置说明

### config.yml
```yaml
# 聊天记录显示格式
format: "&7[<time>] &f<prefix><n>&7: &f<msg>"

# 服务器显示名称映射
servers:
  lobby: "&a大厅"
  survival: "&2生存"

# 忽略记录的命令前缀
ignores:
  - "/login"
  - "/register"
```

## 命令和权限

### 命令
- `/chatrecord <玩家名> [页数]` - 查看指定玩家的聊天记录

### 权限
- `PKChatrecord.staff` - 允许查看聊天记录（不包括命令）
- `PKChatrecord.admin` - 允许查看所有聊天记录（包括命令）

## 开发信息

- 作者: lliiooll
- 版本: 1.0.0
- 主类: `cn.lliiooll.bc.PKChatRecord`
