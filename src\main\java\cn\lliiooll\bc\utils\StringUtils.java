/*
 * Decompiled with CFR 0.152.
 */
package cn.lliiooll.bc.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;

public class StringUtils {
    public static String replaceAll(String str, Map<String, Object> replaces) {
        AtomicReference<String> s = new AtomicReference<String>(str == null ? "" : str);
        replaces.forEach((key, value) -> {
            if (((String)s.get()).contains((CharSequence)key)) {
                s.set(StringUtils.replaceColor(((String)s.get()).replace((CharSequence)key, value.toString())));
            }
        });
        return s.get();
    }

    public static String random(int size) {
        String str = "abcdefghijklmnopqrstuvwxyz";
        String[] zd = (str.toLowerCase() + str.toUpperCase() + "0123456789").split("");
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < size + 1; ++i) {
            sb.append(zd[random.nextInt(zd.length)]);
        }
        return sb.toString();
    }

    public static String replaceColor(String string) {
        return StringUtils.replaceAll(string, (Map<String, Object>)new HashMap<String, Object>(){
            {
                this.put("&", "\u00a7");
            }
        });
    }

    public static boolean isNumber(String arg) {
        try {
            Integer.parseInt(arg);
            return true;
        }
        catch (Throwable e) {
            return false;
        }
    }
}

