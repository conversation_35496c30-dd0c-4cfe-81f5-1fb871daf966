/*
 * Decompiled with CFR 0.152.
 */
package com.google.protobuf;

import com.google.protobuf.ByteString;
import com.google.protobuf.CodedInputStream;
import com.google.protobuf.ExtensionRegistryLite;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageLite;
import java.io.IOException;

public class LazyFieldLite {
    private static final ExtensionRegistryLite EMPTY_REGISTRY = ExtensionRegistryLite.getEmptyRegistry();
    private ByteString delayedBytes;
    private ExtensionRegistryLite extensionRegistry;
    protected volatile MessageLite value;
    private volatile ByteString memoizedBytes;

    public LazyFieldLite(ExtensionRegistryLite extensionRegistry, ByteString bytes) {
        LazyFieldLite.checkArguments(extensionRegistry, bytes);
        this.extensionRegistry = extensionRegistry;
        this.delayedBytes = bytes;
    }

    public LazyFieldLite() {
    }

    public static LazyFieldLite fromValue(MessageLite value) {
        LazyFieldLite lf = new LazyFieldLite();
        lf.setValue(value);
        return lf;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LazyFieldLite)) {
            return false;
        }
        LazyFieldLite other = (LazyFieldLite)o;
        MessageLite value1 = this.value;
        MessageLite value2 = other.value;
        if (value1 == null && value2 == null) {
            return this.toByteString().equals(other.toByteString());
        }
        if (value1 != null && value2 != null) {
            return value1.equals(value2);
        }
        if (value1 != null) {
            return value1.equals(other.getValue(value1.getDefaultInstanceForType()));
        }
        return this.getValue(value2.getDefaultInstanceForType()).equals(value2);
    }

    public int hashCode() {
        return 1;
    }

    public boolean containsDefaultInstance() {
        return this.memoizedBytes == ByteString.EMPTY || this.value == null && (this.delayedBytes == null || this.delayedBytes == ByteString.EMPTY);
    }

    public void clear() {
        this.delayedBytes = null;
        this.value = null;
        this.memoizedBytes = null;
    }

    public void set(LazyFieldLite other) {
        this.delayedBytes = other.delayedBytes;
        this.value = other.value;
        this.memoizedBytes = other.memoizedBytes;
        if (other.extensionRegistry != null) {
            this.extensionRegistry = other.extensionRegistry;
        }
    }

    public MessageLite getValue(MessageLite defaultInstance) {
        this.ensureInitialized(defaultInstance);
        return this.value;
    }

    public MessageLite setValue(MessageLite value) {
        MessageLite originalValue = this.value;
        this.delayedBytes = null;
        this.memoizedBytes = null;
        this.value = value;
        return originalValue;
    }

    public void merge(LazyFieldLite other) {
        if (other.containsDefaultInstance()) {
            return;
        }
        if (this.containsDefaultInstance()) {
            this.set(other);
            return;
        }
        if (this.extensionRegistry == null) {
            this.extensionRegistry = other.extensionRegistry;
        }
        if (this.delayedBytes != null && other.delayedBytes != null) {
            this.delayedBytes = this.delayedBytes.concat(other.delayedBytes);
            return;
        }
        if (this.value == null && other.value != null) {
            this.setValue(LazyFieldLite.mergeValueAndBytes(other.value, this.delayedBytes, this.extensionRegistry));
            return;
        }
        if (this.value != null && other.value == null) {
            this.setValue(LazyFieldLite.mergeValueAndBytes(this.value, other.delayedBytes, other.extensionRegistry));
            return;
        }
        this.setValue(this.value.toBuilder().mergeFrom(other.value).build());
    }

    public void mergeFrom(CodedInputStream input, ExtensionRegistryLite extensionRegistry) throws IOException {
        if (this.containsDefaultInstance()) {
            this.setByteString(input.readBytes(), extensionRegistry);
            return;
        }
        if (this.extensionRegistry == null) {
            this.extensionRegistry = extensionRegistry;
        }
        if (this.delayedBytes != null) {
            this.setByteString(this.delayedBytes.concat(input.readBytes()), this.extensionRegistry);
            return;
        }
        try {
            this.setValue(this.value.toBuilder().mergeFrom(input, extensionRegistry).build());
        }
        catch (InvalidProtocolBufferException invalidProtocolBufferException) {
            // empty catch block
        }
    }

    private static MessageLite mergeValueAndBytes(MessageLite value, ByteString otherBytes, ExtensionRegistryLite extensionRegistry) {
        try {
            return value.toBuilder().mergeFrom(otherBytes, extensionRegistry).build();
        }
        catch (InvalidProtocolBufferException e) {
            return value;
        }
    }

    public void setByteString(ByteString bytes, ExtensionRegistryLite extensionRegistry) {
        LazyFieldLite.checkArguments(extensionRegistry, bytes);
        this.delayedBytes = bytes;
        this.extensionRegistry = extensionRegistry;
        this.value = null;
        this.memoizedBytes = null;
    }

    public int getSerializedSize() {
        if (this.memoizedBytes != null) {
            return this.memoizedBytes.size();
        }
        if (this.delayedBytes != null) {
            return this.delayedBytes.size();
        }
        if (this.value != null) {
            return this.value.getSerializedSize();
        }
        return 0;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public ByteString toByteString() {
        if (this.memoizedBytes != null) {
            return this.memoizedBytes;
        }
        if (this.delayedBytes != null) {
            return this.delayedBytes;
        }
        LazyFieldLite lazyFieldLite = this;
        synchronized (lazyFieldLite) {
            if (this.memoizedBytes != null) {
                return this.memoizedBytes;
            }
            this.memoizedBytes = this.value == null ? ByteString.EMPTY : this.value.toByteString();
            return this.memoizedBytes;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected void ensureInitialized(MessageLite defaultInstance) {
        if (this.value != null) {
            return;
        }
        LazyFieldLite lazyFieldLite = this;
        synchronized (lazyFieldLite) {
            if (this.value != null) {
                return;
            }
            try {
                if (this.delayedBytes != null) {
                    MessageLite parsedValue;
                    this.value = parsedValue = defaultInstance.getParserForType().parseFrom(this.delayedBytes, this.extensionRegistry);
                    this.memoizedBytes = this.delayedBytes;
                } else {
                    this.value = defaultInstance;
                    this.memoizedBytes = ByteString.EMPTY;
                }
            }
            catch (InvalidProtocolBufferException e) {
                this.value = defaultInstance;
                this.memoizedBytes = ByteString.EMPTY;
            }
        }
    }

    private static void checkArguments(ExtensionRegistryLite extensionRegistry, ByteString bytes) {
        if (extensionRegistry == null) {
            throw new NullPointerException("found null ExtensionRegistry");
        }
        if (bytes == null) {
            throw new NullPointerException("found null ByteString");
        }
    }
}

