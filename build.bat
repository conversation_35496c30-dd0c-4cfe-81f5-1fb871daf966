@echo off
echo Building PKChatRecord...
echo.

REM Check if <PERSON><PERSON> is installed
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: <PERSON><PERSON> is not installed or not in PATH
    echo Please install <PERSON><PERSON> from https://maven.apache.org/download.cgi
    echo and add it to your system PATH
    pause
    exit /b 1
)

echo Maven found, starting build...
echo.

REM Clean and compile
echo [1/3] Cleaning previous build...
mvn clean

echo.
echo [2/3] Compiling source code...
mvn compile

echo.
echo [3/3] Packaging JAR file...
mvn package

echo.
if exist target\PKChatRecord-1.0.0.jar (
    echo Build successful!
    echo Output file: target\PKChatRecord-1.0.0.jar
) else (
    echo Build failed! Please check the error messages above.
)

echo.
pause
